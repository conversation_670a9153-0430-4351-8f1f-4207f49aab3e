import JobBooking from '@/components/booking/job_booking.vue';
import DriverMessageComponent from '@/components/common/driver_message_component/driver_message_component.vue';
// import UnassignedPudMaintenance from '@/components/common/unassigned_pud_maintenance/index.vue';
import FleetTracking from '@/components/operations/FleetTracking/index.vue';
import OperationsFleetList from '@/components/operations/OperationDashboard/components/DriverList/operations_fleet_list.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import JobNoteDialog from '@/components/operations/OperationDashboard/components/JobNoteDialog/job_note_dialog.vue';
import UnassignedPudSearch from '@/components/operations/OperationDashboard/components/UnassignedPudItemSearch/index.vue';
import OperationDashboard from '@/components/operations/OperationDashboard/operations_dashboard.vue';
import {
  LOCAL_STORAGE_DASHBOARD_SETTINGS,
  LOCAL_STORAGE_JOB_LIST_GROUPING,
  initJobListGroupingFromLocalStorage,
  initOperationsDashboardSettingsFromLocalStorage,
} from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { applyAttachmentUpdateToJob } from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { addClientReferencesToJobReferences } from '@/helpers/JobBooking/JobReferenceHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnInitialTripRateInfo } from '@/helpers/RateHelpers/TripRateHelpers';
import { setJobRouteProgress } from '@/helpers/RouteHelpers/JobRouteHelpers';
import { defaultNavigationData } from '@/helpers/RouteHelpers/RouteHelpers';
import { initialiseUnassignedPudListFromResponse } from '@/helpers/UnassignedPudItemHelpers/UnassignedPudItemHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import CashSalesClientSelectItem from '@/interface-models/Client/ClientDetails/CashSalesClientSelectItem';
import { ClientDetails } from '@/interface-models/Client/ClientDetails/ClientDetails';
import {
  ClientRelatedContact,
  getClientRelatedContactsByClientId,
} from '@/interface-models/Client/ClientRelatedContact';
import { ApproveClientJobResponse } from '@/interface-models/Generic/ApproveClientJobResponse';
import AddAttachmentToJob from '@/interface-models/Generic/Attachment/AddAttachmentToJob';
import NavigationPanelData from '@/interface-models/Generic/NavigationPanelData/NavigationPanelData';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { INotificationProducer } from '@/interface-models/Generic/NotificationMessage/NotificationProducerInterface';
import GpsMarkerDetails from '@/interface-models/Generic/Position/GpsMarkerDetails';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import { CashSaleClientDetails } from '@/interface-models/Jobs/CashSalesDetails/CashSalesDetails';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { BookJobWithUnassignedPudConfig } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceDialogConfig';
import UnassignedPudItemResponse from '@/interface-models/Jobs/PUD/UnassignedPudItem/SaveUnassignedPudItemResponse';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { UnassignedPudItemRequest } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemRequest';
import { UnassignedPudItemStatus } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemStatus';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import TripRateInformation from '@/interface-models/ServiceRates/TripRateInformation';
import { PudStatusUpdate } from '@/interface-models/Status/PudStatusUpdate';
import { ClientPerson } from '@/interface-models/User/ClientPerson';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { Component, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {
    FleetTracking,
    OperationDashboard,
    DriverMessageComponent,
    OperationsFleetList,
    JobNoteDialog,
    JobDetailsDialog,
    // UnassignedPudMaintenance,
    UnassignedPudSearch,
    JobBooking,
  },
})
export default class BookJobIndex extends Vue implements INotificationProducer {
  public readonly componentTitle: string = 'Operations';

  public serviceRateStore = useServiceRateStore();
  public fuelLevyStore = useFuelLevyStore();
  public companyDetailsStore = useCompanyDetailsStore();
  public dataImportStore = useDataImportStore();
  public appNavigationStore = useAppNavigationStore();
  public operationsStore = useOperationsStore();
  public clientDetailsStore = useClientDetailsStore();
  public filterStore = useFilterStore();
  public sessionManager = sessionManager;

  public userManagementStore = useUserManagementStore();
  public editJob: boolean = false;
  public jobDetails: JobDetails = new JobDetails();
  public jobDetailsSetCounter: number = 0;
  public clientDetails: ClientDetails | null = null;
  public mergedClientServiceRateTable: ClientServiceRate | null = null;
  public clientFuelSurcharges: ClientFuelSurchargeRate[] | null = null;
  public summaryView: boolean = false;
  public viewSelectedJob: boolean = false;
  public selectedJobFromFinalised: number = 0;
  public editingCompletedJob: boolean = false;
  public tripRateInformation: TripRateInformation = new TripRateInformation();

  public allocatedRequestServiceTypeId: number = -1;
  public allocatedRequestRateTypeId: number = -1;

  public lastBroadcastPublishEpoch: number = 0;
  public fleetTrackingGpsQueue: Map<string, GpsMarkerDetails> = new Map<
    string,
    GpsMarkerDetails
  >();

  public bookJobWithUnassignedPudConfig: BookJobWithUnassignedPudConfig | null =
    null;
  public unassignedPudListForClient: UnassignedPudItem[] | null = null;

  public setRouteDataForEditedJob(orsRoute: ORSRoute | null) {
    if (!orsRoute) {
      this.jobDetails.getPlannedRoute();
      return;
    }
    this.jobDetails.plannedRoute = orsRoute;
  }

  public setCurrentClientFuelSurcharges(
    clientFuelSurcharges: ClientFuelSurchargeRate[],
  ) {
    this.clientFuelSurcharges = clientFuelSurcharges;
  }

  public setCurrentActiveMergedServiceRate(
    mergedServiceRate: ClientServiceRate,
  ) {
    this.mergedClientServiceRateTable = mergedServiceRate;
  }

  public async setSelectedClient(clientDetails: ClientDetails): Promise<void> {
    this.clientDetails = clientDetails;
    if (!this.clientDetails) {
      return;
    }
    // Check if currently creating a NEW job from UPI Config
    const isBookingNewJobWithPudConfig =
      this.operationsStore.bookJobWithUnassignedPudConfig?.operationType ===
      JobOperationType.NEW;
    // If the client has the flag of "SEE ACCOUNTS" we should block the job booking and alert the user.
    if (
      clientDetails.accountsReceivable.creditStatus === 2 &&
      !clientDetails.statusList.includes(3) &&
      (!this.viewSelectedJob || isBookingNewJobWithPudConfig)
    ) {
      const dialogNotificationMessage =
        'Booking unavailable for ' +
        (clientDetails.tradingName
          ? clientDetails.tradingName
          : clientDetails.clientName) +
        '. Please see Accounts.';
      useRootStore().setDialogNotification([dialogNotificationMessage]);
      this.savedJob();
      return;
    }

    const clientId = clientDetails.clientId;
    this.requestRateDataForClientId(clientId);

    this.getClientPersonContacts();
    this.getClientCommonAddressList();
    this.getClientRelatedContacts();

    this.setTabStrokeListener(false);
    useRootStore().setGlobalLoader(false);
    if (!this.viewSelectedJob || isBookingNewJobWithPudConfig) {
      this.jobDetails = new JobDetails();
      this.jobDetailsSetCounter++;
      this.tripRateInformation = new TripRateInformation();
      this.jobDetails.client.clientName = JSON.parse(
        JSON.stringify(
          clientDetails.tradingName
            ? clientDetails.tradingName
            : clientDetails.clientName,
        ),
      );

      this.jobDetails.client.id = JSON.parse(
        JSON.stringify(clientDetails.clientId),
      );
      if (clientDetails.proofOfDelivery) {
        this.jobDetails.proofOfDelivery = clientDetails.proofOfDelivery;
      }
      // Add any client references to the job references
      addClientReferencesToJobReferences(
        this.jobDetails.jobReference,
        clientDetails.references.mainJobScreen,
      );
      const clientNotes = JSON.parse(
        JSON.stringify(this.clientDetails.specialInstructions),
      );

      for (const note of clientNotes) {
        if (!note.id) {
          note.id = uuidv4();
        }
      }
      this.jobDetails.notes = clientNotes;
    }
    this.viewSelectedJob = false;
  }

  // Make request to get this clients contacts
  public getClientPersonContacts() {
    if (!this.clientDetails || !this.clientDetails._id) {
      return;
    }

    // force fetch client contacts list
    this.userManagementStore.getClientPersonsWithAuthDetails(
      this.clientDetails._id,
      this.clientDetails.clientPersonDispatchers,
      true,
    );
  }

  public async getClientCommonAddressList() {
    if (!this.clientDetails || !this.clientDetails.clientId) {
      return;
    }
    // fetch client common addresses
    const commonAddresses: ClientCommonAddress[] | null =
      await this.clientDetailsStore.getClientCommonAddressesByClientId(
        this.clientDetails.clientId,
      );
    if (!commonAddresses) {
      console.error(
        "Something went wrong when fetching client's common addresses.",
      );
      return;
    }
  }

  public async getClientRelatedContacts() {
    if (!this.clientDetails || !this.clientDetails.clientId) {
      return;
    }
    const relatedContacts: ClientRelatedContact[] | null =
      await getClientRelatedContactsByClientId(this.clientDetails.clientId);
    if (!relatedContacts) {
      console.error(
        "Something went wrong when fetching client's site contacts.",
      );
      return;
    }
  }

  public async setSelectedClientDetails(clientId: string) {
    // CS === cash sales client. We do not want to request this client because it does not exist in the database. instead we need to initialise the process for cash sales client information gathering
    if (clientId !== 'CS') {
      useRootStore().setGlobalLoader(true);
      this.getAndSetClientDetails(clientId);
      this.requestUnassignedPudListForClient(clientId);
    } else {
      this.setCashSalesClient();
    }
  }

  /**
   * Requests client details by client ID, awaits the response and handles it.
   * @param clientId clientId to request
   */
  public async getAndSetClientDetails(clientId: string) {
    // Request ClientDetails and handle response
    const clientDetails =
      await this.clientDetailsStore.requestClientDetailsByClientId(clientId);
    if (clientDetails) {
      this.setSelectedClient(clientDetails);
    }
  }

  /**
   * Fetches the client's service rates and fuel surcharge for the current date.
   * If none are found, division default rates for both are returned.
   * @param clientId the client ID to fetch rates for
   */
  public async requestRateDataForClientId(clientId?: string) {
    const searchDate = moment().valueOf();
    // Request client service rates. Try first to find client's custom merged
    // rate card. If we can't find that, use division defaults
    const { clientRates, clientFuelRates } =
      await this.serviceRateStore.getCurrentRatesAndFuelForClientId(
        clientId,
        searchDate,
      );
    // Handle service rate response, or show notification if no rates found
    if (clientRates?.clientServiceRate) {
      this.setCurrentActiveMergedServiceRate(clientRates.clientServiceRate);
    }
    // Handle fuel response, or show notification if no fuel surcharge found
    if (clientFuelRates) {
      this.setCurrentClientFuelSurcharges(clientFuelRates);
    }
  }

  public async setCashSalesClient() {
    this.requestRateDataForClientId('CS');
    this.clientDetails = null;
    this.setTabStrokeListener(false);
    if (!this.viewSelectedJob) {
      const cashSalesClient = new CashSalesClientSelectItem();
      this.jobDetails = new JobDetails();
      this.jobDetailsSetCounter++;
      this.tripRateInformation = new TripRateInformation();
      this.jobDetails.client.clientName = cashSalesClient.tradingName;
      this.jobDetails.client.id = cashSalesClient.clientId;
      this.jobDetails.jobReference.push(new JobReferenceDetails(4, ''));
      this.jobDetails.clientDispatcher = new ClientPerson();
      this.jobDetails.cashSaleClientDetails = new CashSaleClientDetails();
      this.jobDetails.proofOfDelivery.pudPaperwork = false;
      this.jobDetails.proofOfDelivery.jobPaperwork = false;
    }
    this.viewSelectedJob = false;
  }

  public tabStrokeHandler(stroke: any) {
    if (stroke.keyCode === 9) {
      stroke.preventDefault();
    }
  }

  public setTabStrokeListener(tabValue: boolean) {
    if (tabValue) {
      window.addEventListener('keydown', this.tabStrokeHandler);
    } else {
      window.removeEventListener('keydown', this.tabStrokeHandler);
      const element = document.getElementById('client-job-select');
      if (element !== null) {
        element.focus();
      }
    }
  }

  public indexChange(index: number): void {
    this.currentIndex = index;
  }

  get navigationData(): NavigationPanelData {
    const currentRouteName = this.$route.name;
    if (currentRouteName) {
      const foundData = defaultNavigationData.find(
        (data) => data.route === currentRouteName,
      );
      if (foundData) {
        return foundData;
      }
    }
    return defaultNavigationData[0];
  }

  get currentIndex() {
    const currentId = this.appNavigationStore.currentComponentId;
    const foundIndex = this.navigationData.children.findIndex(
      (item) => item.id === currentId,
    );
    if (foundIndex !== -1) {
      return foundIndex;
    } else {
      return 0;
    }
  }

  set currentIndex(index: number) {
    const data = this.navigationData.children[index];
    this.appNavigationStore.setCurrentComponentId(data.id);
  }

  public setSummaryView(summaryValue: boolean) {
    this.summaryView = summaryValue;
  }

  // global close function - return to book job index
  public savedJob(): void {
    this.jobDetails = new JobDetails();
    this.jobDetailsSetCounter++;
    this.tripRateInformation = new TripRateInformation();
    this.clientFuelSurcharges = null;
    this.clientDetails = null;
    this.mergedClientServiceRateTable = null;
    this.editingCompletedJob = false;
    this.editJob = false;

    this.unassignedPudListForClient = null;
    this.bookJobWithUnassignedPudConfig = null;
    this.operationsStore.setBookJobWithUnassignedPudConfig(null);
    this.dataImportStore.setCreatingJobFromUnassignedPudItems(false);
  }

  // global close function - return to book job index
  public savedRecurringJobTemplate(): void {
    this.jobDetails = new JobDetails();
    this.jobDetailsSetCounter++;
    this.tripRateInformation = new TripRateInformation();
    this.clientDetails = null;
    this.editingCompletedJob = false;
    this.editJob = false;
  }

  public showJobMaintenance(job: JobDetails, isEditJob: boolean = true): void {
    this.currentIndex = 2;
    this.viewSelectedJob = true;
    this.editJob = isEditJob;
    this.jobDetails = initialiseJobDetails(job);
    this.jobDetailsSetCounter++;

    this.editingCompletedJob = isEditJob
      ? this.jobDetails.workStatus >= WorkStatus.DRIVER_COMPLETED
      : false;

    if (!this.jobDetails.plannedRoute) {
      this.jobDetails.getPlannedRoute();
    }

    this.tripRateInformation = returnInitialTripRateInfo(
      this.jobDetails.accounting,
    );
    this.summaryView = true;

    if (this.jobDetails.client.id === 'CS') {
      this.setCashSalesClient();
      return;
    }
    this.setSelectedClientDetails(job.client.id);

    if (
      !this.jobDetails.plannedRoute ||
      !this.jobDetails.plannedRoute.routes ||
      !this.jobDetails.plannedRoute.routes.length
    ) {
      this.jobDetails.getPlannedRoute();
    }
  }

  get selectedBookingScreenJobId() {
    const jobId = this.operationsStore.selectedBookingScreenJobId;
    return jobId;
  }

  @Watch('selectedBookingScreenJobId')
  public showSelectedJobInBookingScreen() {
    if (this.selectedBookingScreenJobId !== 0) {
      if (
        this.selectedBookingScreenJobId ===
        this.operationsStore.selectedJobDetails?.jobId
      ) {
        this.showJobMaintenance(this.operationsStore.selectedJobDetails);
      }
    } else {
      this.savedJob();
    }
  }

  get reloadJobInBookingScreen(): number {
    return this.operationsStore.reloadJobInBookingScreen;
  }
  get reloadUnassignedPudJobInBookingScreen(): number {
    return this.operationsStore.reloadUnassignedPudJobInBookingScreen;
  }

  get viewingUnassignedPudSearchDialog(): boolean {
    return this.operationsStore.viewingPudSearchDialog;
  }

  // Because the user can cancel a job that is viewed in the booking screen we
  // need to reinitialise the job details if the user selected to edit a job
  // that is already the active selectedJobDetails.
  @Watch('reloadJobInBookingScreen')
  public reloadLastViewedJob() {
    if (
      this.operationsStore.selectedJobDetails &&
      this.selectedBookingScreenJobId ===
        this.operationsStore.selectedJobDetails.jobId
    ) {
      this.showJobMaintenance(this.operationsStore.selectedJobDetails);
    }
  }
  // Whenever the bookJobWithUnassignedPudConfig changes in the store,
  // we should refresh the book job screen
  @Watch('reloadUnassignedPudJobInBookingScreen')
  public reloadUnassignedPudJob() {
    const config = this.operationsStore.bookJobWithUnassignedPudConfig;
    if (
      config === null ||
      !this.dataImportStore.creatingJobFromUnassignedPudItems
    ) {
      // Cancellation of unassigned pud type job
      return;
    }
    this.bookJobWithUnassignedPudConfig = config;
    let jobDetails;
    if (config.operationType === JobOperationType.NEW) {
      jobDetails = new JobDetails();

      jobDetails.client.id = config.clientId;
      const foundClient = this.clientDetailsStore.clientSummaryList.find(
        (c) => c.clientId === config.clientId,
      );
      if (foundClient) {
        jobDetails.client.clientName = foundClient.tradingName
          ? foundClient.tradingName
          : foundClient.clientName;
      }
      this.showJobMaintenance(jobDetails, false);
    } else if (config.operationType === JobOperationType.EDIT) {
      const jobId = config.jobId;
      if (!jobId) {
        console.error('No job ID supplied for edit.');
        return;
      }
      this.getJobDetailsForJobIds([jobId]);
    }
  }

  /**
   * Searches for full JobDetails documents using the provided list of jobIds,
   * converts to accounting summaries and sets to jobList
   * @param jobIds jobIds to query for
   */
  public async getJobDetailsForJobIds(jobIds: number[]) {
    const results = await useJobStore().getJobDetailsForJobIds(jobIds);
    if (results) {
      const jobDetails = results.length === 1 ? results[0] : null;
      if (!jobDetails) {
        // this means that the job search request was utilised in another component.
        // Most likely asset_allocation_details
        return;
      }
      this.showJobMaintenance(jobDetails);
    }
  }

  // when a user selects a job to add an unassigned pud to we require the job search from watcher
  // in reloadUnassignedPudJobInBookingScreen. When we get the response we set the job in the booking screen
  public receivedJobDetailsFromSearch(jobDetailsList: JobDetails[]) {
    const jobDetails = jobDetailsList.length === 1 ? jobDetailsList[0] : null;
    if (!jobDetails) {
      // this means that the job search request was utilised in another component.
      // Most likely asset_allocation_details
      return;
    }
    this.showJobMaintenance(jobDetails);
  }

  // Request a list of all Unassigned Pud Items for the selected client
  public async requestUnassignedPudListForClient(clientId: string) {
    const request: UnassignedPudItemRequest = {
      createdStartEpoch: null,
      createdEndEpoch: null,
      pudStartEpoch: null,
      pudEndEpoch: null,
      clientId,
      assignedStatus: [
        UnassignedPudItemStatus.UNASSIGNED,
        UnassignedPudItemStatus.ASSIGNED,
      ],
      groupReferenceId: null,
      clientSuppliedId: null,
      jobId: null,
    };
    const result =
      await this.dataImportStore.requestUnassignedPudItemList(request);
    this.handleUnassignedPudItemListResponse(result);
  }

  /**
   * Handle response for requestUnassignedPudListForClient. If the response is
   * valid, set the unassignedPudListForClient to the response. If the response
   * is missing unassigned puds, request the missing puds and add them to the
   * current list.
   */
  public async handleUnassignedPudItemListResponse(
    response: UnassignedPudItemResponse | null,
  ) {
    if (!response?.unassignedPudItemList) {
      return;
    }
    this.unassignedPudListForClient = response.unassignedPudItemList;
    const currentUnassignedPudIds: string[] = this.jobDetails.pudItems
      .filter((p) => (p.unassignedPudItemReference?.length ?? 0) > 0)
      .flatMap((p) => p.unassignedPudItemReference ?? []);

    // get a list of all unassigned puds that are required but not yet loaded in the client.
    const missingUnassignedPud_ids: string[] = [];
    for (const unassignedPud_id of currentUnassignedPudIds) {
      const unassignedPudItemExistsLocally =
        this.unassignedPudListForClient.find(
          (x: UnassignedPudItem) => x.id === unassignedPud_id,
        );

      if (!unassignedPudItemExistsLocally) {
        missingUnassignedPud_ids.push(unassignedPud_id);
      }
    }

    // If there are no unassigned puds that are missing we can show the dialog
    if (missingUnassignedPud_ids.length === 0) {
      this.unassignedPudListForClient = initialiseUnassignedPudListFromResponse(
        response.unassignedPudItemList,
      );
      return;
    }

    // request the unassigned puds that we do not yet have in local state.
    this.handleRemainingUnassignedPudItemListResponse(
      await this.dataImportStore.getUnassignedPudsBy_idList(
        missingUnassignedPud_ids,
      ),
    );
  }

  // response handler for getUnassignedPudsBy_idList(missingUnassignedPud_ids)
  // containing a list of unassigned puds. We will add these unassigned puds to
  // our current list (unassignedPudListForClient)
  public handleRemainingUnassignedPudItemListResponse(
    unassignedPudItems: UnassignedPudItem[] | null,
  ) {
    unassignedPudItems ??= [];
    if (!this.unassignedPudListForClient) {
      return;
    }
    this.unassignedPudListForClient =
      this.unassignedPudListForClient.concat(unassignedPudItems);
    this.unassignedPudListForClient = initialiseUnassignedPudListFromResponse(
      this.unassignedPudListForClient,
    );
  }

  get applicationHasLoaded() {
    return useRootStore().applicationHasLoaded;
  }

  get middleSectionWidth() {
    if (
      this.currentIndex === 1 ||
      this.currentIndex === 3 ||
      this.currentIndex === 4 ||
      this.currentIndex === 5 ||
      this.currentIndex === 6 ||
      this.currentIndex === 7
    ) {
      return 'lg9 md9';
    } else if (this.currentIndex === 2) {
      return 'lg12 md12';
    } else {
      return 'lg7 md9';
    }
  }

  get sidePanelWidth() {
    if (
      this.currentIndex === 1 ||
      this.currentIndex === 2 ||
      this.currentIndex === 3 ||
      this.currentIndex === 4 ||
      this.currentIndex === 5 ||
      this.currentIndex === 6 ||
      this.currentIndex === 7
    ) {
      return 'lg3 md3';
    } else {
      return 'lg5 md3';
    }
  }

  @Watch('currentIndex')
  public screenIndexChange(indexValue: number) {
    this.setNavigationContent(indexValue);
  }

  public setNavigationContent(index: number) {
    this.navigationData.subHeading =
      this.navigationData.children[index].subHeading;
  }
  public updatePudListOrder(pudItems: PUDItem[]) {
    this.jobDetails.pudItems = pudItems;
  }

  public trackingMapWasOpened() {
    this.currentIndex = 0;
  }

  // =========================================================================
  // FLEET TRACKING WINDOW PULL OUT
  // =========================================================================

  get fleetAssetTrackingWindowOpen() {
    return useFleetMapStore().isFleetTrackingWindowOpen;
  }

  // =========================================================================
  // Job notes dialog logic
  // =========================================================================

  get showJobNoteDialog() {
    return this.operationsStore.viewingJobNotesDialog;
  }

  // ===========================================================================
  // Job Details Dialog Logic
  // ===========================================================================

  get showJobDetailsDialog() {
    return this.operationsStore.viewingJobDetailsDialog;
  }

  get selectedJobDetails() {
    return this.operationsStore.selectedJobDetails &&
      this.operationsStore.selectedJobDetails.jobId ===
        this.operationsStore.selectedJobId
      ? this.operationsStore.selectedJobDetails
      : null;
  }
  // ===========================================================================
  // PUD MAINTENANCE DIALOG
  // ===========================================================================
  get showPudMaintenanceDialog() {
    return false;
    // return this.operationsStore.viewingPudMaintenanceDialog;
  }

  // Trigger app notification. Defaults to ERROR type message, but type can be
  // provided to produce other types. Includes componentTitle as a title for the
  // notification.
  public showAppNotification(text: string, type?: HealthLevel): void {
    showNotification(text, {
      type,
      title: this.componentTitle,
    });
  }

  /**
   * Mitt callback for jobStatusUpdate, updatedStatusJobDetails and
   * updateJobEventListResponse events. Used to update the currently editing job
   * such that it's kept in sync with state changes.
   * @param payload JobEventSummary payload
   */
  private handleJobStatusUpdate(payload: JobEventSummary | null) {
    if (!payload || payload.jobId !== this.jobDetails.jobId) {
      return;
    }
    if (!payload.jobDetails && payload.statusList && payload.latestEvent) {
      this.jobDetails.statusList = payload.statusList;
      this.jobDetails.workStatus = payload.workStatus;
      this.jobDetails.eventList.push(payload.latestEvent);
    }
    if (payload.event === 'UpdateJobDetails') {
      if (payload.jobDetails) {
        this.showAppNotification(
          'This job was recently changed. To minimise conflicts, any pending changes were reset.',
        );
        this.showJobMaintenance(payload.jobDetails);
      }
      return;
    }
    if (payload.event === 'CompletedJob') {
      this.editingCompletedJob = true;
    }
    if (payload.jobDetails) {
      this.jobDetails.statusList = payload.jobDetails.statusList;
      this.jobDetails.eventList = payload.jobDetails.eventList;
      this.jobDetails.fleetAssetId = payload.jobDetails.fleetAssetId;
      this.jobDetails.driverId = payload.jobDetails.driverId;
      this.jobDetails.workStatus = payload.jobDetails.workStatus;
    }
  }

  /**
   * Mitt callback for pudStatusUpdate event. Used to update the currently
   * editing job such that it's kept in sync with state changes.
   * @param payload pudStatusUpdate payload
   */
  private handlePudStatusUpdate(payload: PudStatusUpdate | null) {
    if (!payload || payload.jobId !== this.jobDetails.jobId) {
      return;
    }

    // Check if the order of the puds have changed. If so, we'll boot them from
    // the booking screen because there will have been too many changes.
    let originalIndex = -1;
    if (this.operationsStore.selectedJobDetails) {
      originalIndex =
        this.operationsStore.selectedJobDetails.pudItems.findIndex(
          (pud: PUDItem) => pud.pudId === payload.pudId,
        );
    }
    const currentIndex = this.jobDetails.pudItems.findIndex(
      (pud: PUDItem) => pud.pudId === payload.pudId,
    );
    // If index is different, show notification
    if (
      currentIndex !== originalIndex &&
      this.operationsStore.selectedJobDetails
    ) {
      this.showAppNotification(
        'This job was recently changed. To minimise conflicts, any pending changes were reset.',
      );
      this.showJobMaintenance(this.operationsStore.selectedJobDetails);
      return;
    }
    // Set pud status
    this.jobDetails.pudItems[currentIndex].status = payload.pudStatus ?? null;

    // Construct event and add to event list
    const jobStatusUpdate: JobStatusUpdate = Object.assign(
      new JobStatusUpdate(),
      {
        jobId: payload.jobId,
        pudId: payload.pudId,
        changeTime: payload.changeTime,
        updatedStatus: payload.pudStatus ? payload.pudStatus : '',
        editedBy: payload.editedBy,
      },
    );
    this.jobDetails.eventList.push(jobStatusUpdate);

    // If we're on the last pud, the job route progress won't be
    // updated by a Matrix Info API call, so we need to update this
    // local copy manually
    if (
      currentIndex === this.jobDetails.pudItems.length - 1 &&
      !!payload.pudStatus
    ) {
      setJobRouteProgress(this.jobDetails, null, true);
    }
  }

  /**
   * Mitt callback for 'addClientEdiEventToJob' event
   * @param update contains jobId and new event list item to add
   */
  private handleNewJobEvent(update: JobStatusUpdate | null) {
    if (!update?.jobId) {
      return;
    }
    if (update.jobId === this.jobDetails.jobId) {
      this.jobDetails.eventList.push(update);
    }
  }

  /**
   * Handles division-level updates for approving a COMPLETED ACTION REQUIRED
   * job
   * @param payload containing jobId and updated status list
   */
  private handleJobConfirmation(payload: ApproveClientJobResponse | null) {
    if (!payload) {
      return;
    }
    if (payload.jobId === this.jobDetails.jobId) {
      this.jobDetails.statusList = payload.statusList;
    }
  }

  /**
   * Handles division-level updates to job allocation
   * @param jobIds list of jobIds that were allocated
   */
  private handleAllocatedJobIds(jobIds: number[] | null) {
    const jobUpdateRequired = (jobIds ?? []).find(
      (x: number) => x === this.jobDetails.jobId,
    );
    if (jobUpdateRequired) {
      this.jobDetails.workStatus = WorkStatus.ALLOCATED;
    }
  }
  /**
   * Mitt callback to handle division-level message for attachment update
   * @param payload containing update details
   */
  private handleAttachmentUpdate(payload: AddAttachmentToJob | null) {
    if (this.jobDetails.jobId && payload?.jobId === this.jobDetails.jobId) {
      applyAttachmentUpdateToJob(payload, this.jobDetails);
    }
  }

  /**
   * Set up mitt listeners for this component. Used to update the job being
   * edited in the book screen when updates come in.
   */
  private setMittListeners() {
    // Turn on mitt listeners
    Mitt.on('allocatedPreAllocatedJobIds', this.handleAllocatedJobIds);
    Mitt.on('updatedJobWithAttachment', this.handleAttachmentUpdate);
    Mitt.on('approveJobCompletedActionRequired', this.handleJobConfirmation);
    Mitt.on('addClientEdiEventToJob', this.handleNewJobEvent);
    Mitt.on('pudStatusUpdate', this.handlePudStatusUpdate);
    Mitt.on('jobStatusUpdate', this.handleJobStatusUpdate);
    Mitt.on('updatedStatusJobDetails', this.handleJobStatusUpdate);
    Mitt.on('updateJobEventListResponse', this.handleJobStatusUpdate);
  }

  /**
   * Destroy mitt listeners when this component unmounts.
   */
  private unsetMittListeners() {
    // Turn off mitt listeners
    Mitt.off('allocatedPreAllocatedJobIds', this.handleAllocatedJobIds);
    Mitt.off('updatedJobWithAttachment', this.handleAttachmentUpdate);
    Mitt.off('approveJobCompletedActionRequired', this.handleJobConfirmation);
    Mitt.off('addClientEdiEventToJob', this.handleNewJobEvent);
    Mitt.off('pudStatusUpdate', this.handlePudStatusUpdate);
    Mitt.off('jobStatusUpdate', this.handleJobStatusUpdate);
    Mitt.off('updatedStatusJobDetails', this.handleJobStatusUpdate);
    Mitt.off('updateJobEventListResponse', this.handleJobStatusUpdate);
  }

  // Before mounting of component, check Local Storage for existing settings and
  // set to store
  public beforeMount() {
    initOperationsDashboardSettingsFromLocalStorage(
      LOCAL_STORAGE_DASHBOARD_SETTINGS,
    );
    initJobListGroupingFromLocalStorage(LOCAL_STORAGE_JOB_LIST_GROUPING);
    this.operationsStore.setSelectedFleetAssetId('');
    this.operationsStore.setSelectedDriverId('');
    this.operationsStore.setViewingJobDetailsDialog(false);
    this.operationsStore.setSelectedJobId(-1);
  }

  public mounted() {
    this.setMittListeners();
  }

  public beforeDestroy() {
    this.operationsStore.setViewingJobDetailsDialog(false);
    this.operationsStore.setViewingPudMaintenanceDialog(false);
    this.operationsStore.setViewingPudSearchDialog(false);
    this.operationsStore.setViewingJobNotesDialog(false);
    this.unsetMittListeners();
  }
}
