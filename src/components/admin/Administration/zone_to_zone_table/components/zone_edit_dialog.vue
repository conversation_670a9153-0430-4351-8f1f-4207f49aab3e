<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="isEditing ? 'Edit Zone' : 'Create New Zone'"
    width="800px"
    max-width="800px"
    contentPadding="ma-0"
    :showActions="false"
    @cancel="closeDialog"
  >
    <v-flex>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <div v-if="dialogController">
            <!-- New Zone Tab -->
            <v-layout row wrap class="body-scrollable--65 pa-3">
              <ZoneForm
                ref="newZoneForm"
                :zoneData="formData"
                :isEditing="false"
                :clientId="clientId"
                @save="onSave"
                @cancel="closeDialog"
              />
            </v-layout>
            <!-- Edit Mode (no tabs) -->
            <v-layout
              v-if="isEditing"
              row
              wrap
              class="body-scrollable--65 pa-3"
            >
              <ZoneForm
                ref="editZoneForm"
                :zoneData="formData"
                :isEditing="true"
                :clientId="clientId"
                @save="onSave"
                @cancel="closeDialog"
              />
            </v-layout>
          </div>
        </v-flex>
      </v-layout>
    </v-flex>
  </ContentDialog>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { UpdateZoneRequest } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/UpdateZoneRequest';
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { ZoneSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneSummary';
import { useZoneToZoneStore } from '@/store/modules/ZoneToZoneStore';
import { computed, ref, watch, WritableComputedRef } from 'vue';
import ZoneForm from './zone_form.vue';

interface Props {
  showDialog: boolean;
  isEditing: boolean;
  zoneData?: ZoneSummary | null;
  clientId: string;
}

const props = withDefaults(defineProps<Props>(), {
  zoneData: null,
});

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'save'): void;
}>();

const zoneToZoneStore = useZoneToZoneStore();

// Reactive data
const createTab = ref(0);
const isSaving = ref(false);
const selectedExistingZone = ref<number | null>(null);
const existingZones = ref<Array<{ zoneId: number; displayName: string }>>([]);

// Form data
const formData = ref<ZoneSummary>({
  clientId: '',
  zoneId: 0,
  zoneName: '',
  zoneLocationsList: [],
});

// Dialog controller
const dialogController: WritableComputedRef<boolean> = computed({
  get() {
    return props.showDialog;
  },
  set(value: boolean) {
    if (!value) {
      emit('close');
    }
  },
});

// Initialize form data when dialog opens or zone data changes
watch([() => props.showDialog, () => props.zoneData], () => {
  if (props.showDialog) {
    initializeFormData();
    if (!props.isEditing) {
      loadExistingZones();
    }
  }
});

const initializeFormData = () => {
  if (props.isEditing && props.zoneData) {
    formData.value = {
      ...props.zoneData,
      zoneLocationsList: [...props.zoneData.zoneLocationsList],
    };
  } else {
    formData.value = {
      clientId: props.clientId,
      zoneId: 0, // Will be set by backend
      zoneName: '',
      zoneLocationsList: [],
    };
  }
  selectedExistingZone.value = null;
  createTab.value = 0;
};

const loadExistingZones = async () => {
  try {
    const zones = await zoneToZoneStore.requestZoneListForClient(
      props.clientId,
    );
    if (zones) {
      existingZones.value = zones.map((zone) => ({
        zoneId: zone.zoneId,
        displayName: `Zone ${zone.zoneId}: ${zone.zoneName}`,
      }));
    }
  } catch (error) {
    console.error('Failed to load existing zones:', error);
    existingZones.value = [];
  }
};

const onLocationsChanged = (locations: ZoneLocationSummary[]) => {
  formData.value.zoneLocationsList = locations;
};

const onSave = async (zoneFormData: ZoneSummary) => {
  isSaving.value = true;
  try {
    const request: UpdateZoneRequest = {
      clientId: props.clientId,
      zoneId: props.isEditing ? zoneFormData.zoneId : 0, // 0 for new zones
      zoneName: zoneFormData.zoneName,
      addedZoneLocations: zoneFormData.zoneLocationsList,
      deletedZoneLocations: [], // Handle deletions in the form component
    };

    const result = await zoneToZoneStore.updateZone(request);
    if (result) {
      emit('save');
    }
  } catch (error) {
    console.error('Failed to save zone:', error);
  } finally {
    isSaving.value = false;
  }
};

const onAddToExisting = async () => {
  if (
    !selectedExistingZone.value ||
    formData.value.zoneLocationsList.length === 0
  ) {
    return;
  }

  isSaving.value = true;
  try {
    const request: UpdateZoneRequest = {
      clientId: props.clientId,
      zoneId: selectedExistingZone.value,
      zoneName: '', // Don't update zone name when adding locations
      addedZoneLocations: formData.value.zoneLocationsList,
      deletedZoneLocations: [],
    };

    const result = await zoneToZoneStore.updateZone(request);
    if (result) {
      emit('save');
    }
  } catch (error) {
    console.error('Failed to add locations to existing zone:', error);
  } finally {
    isSaving.value = false;
  }
};

const closeDialog = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
.dialog-content {
  min-height: 400px;
}

.form-field-required-marker::after {
  content: ' *';
  color: #ff5252;
}

.body-scrollable--65 {
  max-height: 65vh;
  overflow-y: auto;
}
</style>
