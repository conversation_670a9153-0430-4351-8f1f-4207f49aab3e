<template>
  <v-flex md12>
    <v-form ref="zoneForm" v-model="formValid">
      <!-- Zone Name -->
      <v-flex md12>
        <v-layout align-center justify-space-between class="mt-4">
          <h6 class="pr-3 pb-0 form-field-required-marker">Zone Name</h6>
        </v-layout>
        <v-flex md12>
          <v-text-field
            v-model="localZoneData.zoneName"
            label="Zone Name"
            solo
            flat
            class="v-solo-custom"
            :rules="[(v) => !!v || 'Zone name is required']"
            :disabled="isSaving"
          />
        </v-flex>
      </v-flex>

      <!-- Zone Locations -->
      <v-flex md12 class="mt-4">
        <v-layout align-center justify-space-between>
          <h6 class="pr-3 pb-0 form-field-required-marker">Zone Locations</h6>
        </v-layout>

        <ZoneLocationForm
          ref="locationForm"
          :locations="localZoneData.zoneLocationsList"
          :clientId="clientId"
          :disabled="isSaving"
          @locationsChanged="onLocationsChanged"
        />
      </v-flex>

      <!-- Action Buttons -->
      <v-flex md12 class="mt-4">
        <v-layout justify-end>
          <v-btn
            color="grey"
            depressed
            @click="onCancel"
            :disabled="isSaving"
            class="mr-2"
          >
            Cancel
          </v-btn>
          <v-btn
            color="blue"
            depressed
            @click="onSave"
            :loading="isSaving"
            :disabled="
              !formValid || localZoneData.zoneLocationsList.length === 0
            "
          >
            {{ isEditing ? 'Update Zone' : 'Create Zone' }}
          </v-btn>
        </v-layout>
      </v-flex>
    </v-form>
  </v-flex>
</template>

<script setup lang="ts">
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { ZoneSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneSummary';
import { ref, watch } from 'vue';
import ZoneLocationForm from './zone_location_form.vue';

interface Props {
  zoneData: ZoneSummary;
  isEditing: boolean;
  clientId: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'save', zoneData: ZoneSummary): void;
  (e: 'cancel'): void;
}>();

// Reactive data
const formValid = ref(false);
const isSaving = ref(false);
const localZoneData = ref<ZoneSummary>({
  clientId: '',
  zoneId: 0,
  zoneName: '',
  zoneLocationsList: [],
});

// Watch for changes to zoneData prop
watch(
  () => props.zoneData,
  (newData) => {
    if (newData) {
      localZoneData.value = {
        ...newData,
        zoneLocationsList: [...newData.zoneLocationsList],
      };
    }
  },
  { immediate: true, deep: true },
);

const onLocationsChanged = (locations: ZoneLocationSummary[]) => {
  localZoneData.value.zoneLocationsList = locations;
};

const onSave = () => {
  if (formValid.value && localZoneData.value.zoneLocationsList.length > 0) {
    emit('save', { ...localZoneData.value });
  }
};

const onCancel = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.form-field-required-marker::after {
  content: ' *';
  color: #ff5252;
}
</style>
