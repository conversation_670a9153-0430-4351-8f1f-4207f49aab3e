<template>
  <v-flex md12>
    <!-- Add New Location -->
    <v-flex md12 class="mb-3">
      <v-layout align-center>
        <h6 class="pr-3">Add Location</h6>
        <v-spacer />
        <v-btn
          color="blue"
          small
          depressed
          @click="addNewLocation"
          :disabled="disabled"
        >
          <v-icon left small>add</v-icon>
          Add Location
        </v-btn>
      </v-layout>
    </v-flex>

    <!-- New Location Input -->
    <v-flex md12 v-if="showAddForm" class="mb-4">
      <div class="pa-3">
        <v-layout row wrap>
          <v-flex md12>
            <h6 class="mb-2">New Location</h6>
          </v-flex>
          <v-flex md12>
            <AddressSuburbSearch
              :address="newLocationAddress"
              :formDisabled="disabled"
              :setFocus="true"
              :searchAddress="true"
              :enableSuburbSelect="true"
              @suburbSelected="onSuburbSelected"
            />
          </v-flex>
          <v-flex md12 class="mt-2">
            <v-layout justify-end>
              <v-btn
                color="grey"
                small
                depressed
                @click="cancelAddLocation"
                :disabled="disabled"
                class="mr-2"
              >
                Cancel
              </v-btn>
              <v-btn
                color="blue"
                small
                depressed
                @click="confirmAddLocation"
                :disabled="disabled || !isNewLocationValid"
              >
                Add
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-flex>

    <!-- Existing Locations List -->
    <v-flex md12 v-if="localLocations.length > 0">
      <v-layout align-center class="mb-2">
        <h6>Current Locations ({{ localLocations.length }})</h6>
      </v-layout>

      <v-data-table
        :headers="locationHeaders"
        :items="localLocations"
        class="gd-dark-theme"
        hide-actions
        :rows-per-page-items="[10, 25, 50]"
      >
        <template v-slot:items="data">
          <tr>
            <td>{{ data.item.suburb }}</td>
            <td>{{ data.item.postcode }}</td>
            <td>{{ data.item.state }}</td>
            <td>
              <v-btn
                icon
                small
                @click="editLocation(data.index)"
                :disabled="disabled"
              >
                <v-icon small>edit</v-icon>
              </v-btn>
              <v-btn
                icon
                small
                @click="removeLocation(data.index)"
                :disabled="disabled"
                color="error"
              >
                <v-icon small>delete</v-icon>
              </v-btn>
            </td>
          </tr>
        </template>
        <template v-slot:no-data>
          <div class="text-center pa-3">
            No locations added yet. Use the "Add Location" button above.
          </div>
        </template>
      </v-data-table>
    </v-flex>

    <!-- Edit Location Dialog -->
    <v-dialog v-model="showEditDialog" max-width="500px" persistent>
      <v-card>
        <v-card-title>
          <span class="headline">Edit Location</span>
        </v-card-title>
        <v-card-text>
          <v-layout row wrap>
            <v-flex md12>
              <AddressSuburbSearch
                :address="editingLocationAddress"
                :formDisabled="disabled"
                :setFocus="true"
                :searchAddress="true"
                :enableSuburbSelect="true"
                :selectedSuburbProp="editingSuburb"
                @suburbSelected="onEditSuburbSelected"
              />
            </v-flex>
          </v-layout>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey"
            depressed
            @click="cancelEditLocation"
            :disabled="disabled"
          >
            Cancel
          </v-btn>
          <v-btn
            color="blue"
            depressed
            @click="confirmEditLocation"
            :disabled="disabled || !isEditLocationValid"
          >
            Update
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-flex>
</template>

<script setup lang="ts">
import AddressSuburbSearch from '@/components/common/addressing/address-search-au/address_suburb_search.vue';
import { AddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { computed, ref, watch } from 'vue';

interface Props {
  locations: ZoneLocationSummary[];
  clientId: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const emit = defineEmits<{
  (e: 'locationsChanged', locations: ZoneLocationSummary[]): void;
}>();

// Reactive data
const localLocations = ref<ZoneLocationSummary[]>([]);
const showAddForm = ref(false);
const showEditDialog = ref(false);
const editingIndex = ref(-1);

// New location data
const newLocationAddress = ref(new AddressAU());
const newLocationSuburb = ref<SuburbAU | null>(null);

// Edit location data
const editingLocationAddress = ref(new AddressAU());
const editingSuburb = ref<SuburbAU | null>(null);

// Table headers
const locationHeaders = [
  { text: 'Suburb', value: 'suburb', sortable: true },
  { text: 'Postcode', value: 'postcode', sortable: true },
  { text: 'State', value: 'state', sortable: true },
  { text: 'Actions', value: 'actions', sortable: false, width: '120px' },
];

// Computed properties
const isNewLocationValid = computed(() => {
  return (
    newLocationSuburb.value &&
    newLocationSuburb.value.name &&
    newLocationSuburb.value.postcode &&
    newLocationSuburb.value.state
  );
});

const isEditLocationValid = computed(() => {
  return (
    editingSuburb.value &&
    editingSuburb.value.name &&
    editingSuburb.value.postcode &&
    editingSuburb.value.state
  );
});

// Watch for changes to locations prop
watch(
  () => props.locations,
  (newLocations) => {
    localLocations.value = [...newLocations];
  },
  { immediate: true, deep: true },
);

// Watch for changes to local locations and emit
watch(
  localLocations,
  (newLocations) => {
    emit('locationsChanged', [...newLocations]);
  },
  { deep: true },
);

// Methods
const addNewLocation = () => {
  showAddForm.value = true;
  newLocationAddress.value = new AddressAU();
  newLocationSuburb.value = null;
};

const cancelAddLocation = () => {
  showAddForm.value = false;
  newLocationAddress.value = new AddressAU();
  newLocationSuburb.value = null;
};

const onSuburbSelected = (suburb: SuburbAU) => {
  newLocationSuburb.value = suburb;
  newLocationAddress.value.fromSuburb(suburb);
};

const confirmAddLocation = () => {
  if (isNewLocationValid.value && newLocationSuburb.value) {
    const newLocation: ZoneLocationSummary = {
      suburb: newLocationSuburb.value.name,
      postcode: newLocationSuburb.value.postcode,
      state: newLocationSuburb.value.state,
      pudId: '', // Not used for zone management
      zoneName: '', // Will be set by parent
    };

    // Check for duplicates
    const isDuplicate = localLocations.value.some(
      (location) =>
        location.suburb === newLocation.suburb &&
        location.postcode === newLocation.postcode &&
        location.state === newLocation.state,
    );

    if (!isDuplicate) {
      localLocations.value.push(newLocation);
      cancelAddLocation();
    } else {
      // Could show a notification here about duplicate
      console.warn('Location already exists in this zone');
    }
  }
};

const editLocation = (index: number) => {
  editingIndex.value = index;
  const location = localLocations.value[index];

  // Create a suburb object for the address search component
  editingSuburb.value = {
    id: '',
    formattedAddress: `${location.suburb}, ${location.state} ${location.postcode}`,
    name: location.suburb,
    postcode: location.postcode,
    state: location.state,
    geoLocation: [],
  };

  editingLocationAddress.value = new AddressAU();
  editingLocationAddress.value.fromSuburb(editingSuburb.value);

  showEditDialog.value = true;
};

const cancelEditLocation = () => {
  showEditDialog.value = false;
  editingIndex.value = -1;
  editingLocationAddress.value = new AddressAU();
  editingSuburb.value = null;
};

const onEditSuburbSelected = (suburb: SuburbAU) => {
  editingSuburb.value = suburb;
  editingLocationAddress.value.fromSuburb(suburb);
};

const confirmEditLocation = () => {
  if (
    isEditLocationValid.value &&
    editingSuburb.value &&
    editingIndex.value >= 0
  ) {
    const updatedLocation: ZoneLocationSummary = {
      suburb: editingSuburb.value.name,
      postcode: editingSuburb.value.postcode,
      state: editingSuburb.value.state,
      pudId: localLocations.value[editingIndex.value].pudId,
      zoneName: localLocations.value[editingIndex.value].zoneName,
    };

    localLocations.value[editingIndex.value] = updatedLocation;
    cancelEditLocation();
  }
};

const removeLocation = (index: number) => {
  localLocations.value.splice(index, 1);
};
</script>

<style lang="scss" scoped>
.v-card {
  border-radius: 4px;
}
</style>
