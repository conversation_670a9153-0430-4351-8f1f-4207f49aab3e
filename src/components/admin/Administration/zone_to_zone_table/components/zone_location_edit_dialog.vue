<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    title="Edit Zone Location"
    width="500px"
    contentPadding="pa-0"
    @cancel="closeDialog"
    @confirm="onSave"
    :showActions="true"
    :isDisabled="!isFormValid"
    :isLoading="isSaving"
    confirmBtnText="Update Location"
  >
    <v-layout>
      <v-flex md12 class="body-scrollable--75 body-min-height--65 pa-3">
        <v-form ref="locationForm" v-model="formValid">
          <v-flex md12>
            <v-layout align-center justify-space-between class="mt-4">
              <h6 class="pr-3 pb-0 form-field-required-marker">
                Select New Location
              </h6>
            </v-layout>
            <v-flex md12>
              <AddressSuburbSearch
                :address="locationAddress"
                :formDisabled="isSaving"
                :setFocus="true"
                :searchAddress="true"
                :enableSuburbSelect="true"
                :selectedSuburbProp="selectedSuburb"
                @suburbSelected="onSuburbSelected"
              />
            </v-flex>
          </v-flex>

          <!-- Current Location Info -->
          <v-flex md12 class="mt-4" v-if="locationData">
            <v-layout align-center justify-space-between>
              <h6 class="pr-3 pb-0">Current Location</h6>
            </v-layout>
            <v-flex md12>
              <div class="pa-2">
                <div><strong>Suburb:</strong> {{ locationData.suburb }}</div>
                <div>
                  <strong>Postcode:</strong> {{ locationData.postcode }}
                </div>
                <div><strong>State:</strong> {{ locationData.state }}</div>
              </div>
            </v-flex>
          </v-flex>

          <!-- New Location Preview -->
          <v-flex md12 class="mt-4" v-if="selectedSuburb">
            <v-layout align-center justify-space-between>
              <h6 class="pr-3 pb-0">New Location</h6>
            </v-layout>
            <v-flex md12>
              <div class="pa-2">
                <div><strong>Suburb:</strong> {{ selectedSuburb.name }}</div>
                <div>
                  <strong>Postcode:</strong> {{ selectedSuburb.postcode }}
                </div>
                <div><strong>State:</strong> {{ selectedSuburb.state }}</div>
              </div>
            </v-flex>
          </v-flex>
        </v-form>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import AddressSuburbSearch from '@/components/common/addressing/address-search-au/address_suburb_search.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { AddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { computed, ref, watch, WritableComputedRef } from 'vue';

interface Props {
  showDialog: boolean;
  locationData?: ZoneLocationSummary | null;
}

const props = withDefaults(defineProps<Props>(), {
  locationData: null,
});

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'save', updatedLocation: ZoneLocationSummary): void;
}>();

// Reactive data
const formValid = ref(false);
const isSaving = ref(false);
const locationAddress = ref(new AddressAU());
const selectedSuburb = ref<SuburbAU | null>(null);

// Dialog controller
const dialogController: WritableComputedRef<boolean> = computed({
  get() {
    return props.showDialog;
  },
  set(value: boolean) {
    if (!value) {
      emit('close');
    }
  },
});

// Form validation
const isFormValid = computed(() => {
  return (
    formValid.value &&
    selectedSuburb.value &&
    selectedSuburb.value.name &&
    selectedSuburb.value.postcode &&
    selectedSuburb.value.state
  );
});

// Watch for dialog opening and location data changes
watch([() => props.showDialog, () => props.locationData], () => {
  if (props.showDialog && props.locationData) {
    initializeForm();
  }
});

const initializeForm = () => {
  if (props.locationData) {
    // Create a suburb object from the current location data
    selectedSuburb.value = {
      id: '',
      formattedAddress: `${props.locationData.suburb}, ${props.locationData.state} ${props.locationData.postcode}`,
      name: props.locationData.suburb,
      postcode: props.locationData.postcode,
      state: props.locationData.state,
      geoLocation: [],
    };

    locationAddress.value = new AddressAU();
    locationAddress.value.fromSuburb(selectedSuburb.value);
  } else {
    selectedSuburb.value = null;
    locationAddress.value = new AddressAU();
  }
};

const onSuburbSelected = (suburb: SuburbAU) => {
  selectedSuburb.value = suburb;
  locationAddress.value.fromSuburb(suburb);
};

const onSave = () => {
  if (isFormValid.value && selectedSuburb.value && props.locationData) {
    const updatedLocation: ZoneLocationSummary = {
      suburb: selectedSuburb.value.name,
      postcode: selectedSuburb.value.postcode,
      state: selectedSuburb.value.state,
      pudId: props.locationData.pudId,
      zoneName: props.locationData.zoneName,
    };

    emit('save', updatedLocation);
  }
};

const closeDialog = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
.form-field-required-marker::after {
  content: ' *';
  color: #ff5252;
}

.body-scrollable--75 {
  max-height: 75vh;
  overflow-y: auto;
}

.body-min-height--65 {
  min-height: 65vh;
}
</style>
