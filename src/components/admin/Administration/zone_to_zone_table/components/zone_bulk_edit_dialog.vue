<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="`Bulk Edit Zones (${selectedZones.length} items)`"
    width="700px"
    contentPadding="ma-0"
    :showActions="false"
    @cancel="closeDialog"
  >
    <div>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <div v-if="dialogController">
            <v-layout row wrap class="body-scrollable--65 pa-3">
              <v-flex md12>
                <v-alert :value="true" type="info" class="mb-3">
                  You are editing {{ selectedZones.length }} zone(s). Changes
                  will be applied to all selected zones.
                </v-alert>
              </v-flex>

              <!-- Zone Name Update -->
              <v-flex md12>
                <v-layout align-center justify-space-between class="mt-4">
                  <h6 class="pr-3 pb-0">Zone Name Update</h6>
                </v-layout>
                <v-flex md12>
                  <v-text-field
                    v-model="bulkEditData.zoneName"
                    label="New Zone Name (Optional - leave empty to keep existing names)"
                    solo
                    flat
                    class="v-solo-custom"
                    :disabled="isSaving"
                    hint="If provided, all selected zones will be renamed to this value"
                    persistent-hint
                  />
                </v-flex>
              </v-flex>

              <!-- Location Management -->
              <v-flex md12 class="mt-4">
                <v-layout align-center justify-space-between>
                  <h6 class="pr-3 pb-0">Location Management</h6>
                </v-layout>

                <v-flex md12 class="mt-2">
                  <v-radio-group
                    v-model="bulkEditData.locationAction"
                    :disabled="isSaving"
                  >
                    <v-radio
                      label="Add locations to selected zones"
                      value="add"
                    />
                    <v-radio
                      label="Remove locations from selected zones"
                      value="remove"
                    />
                    <v-radio label="No location changes" value="none" />
                  </v-radio-group>
                </v-flex>

                <!-- Location Selection -->
                <v-flex md12 v-if="bulkEditData.locationAction !== 'none'">
                  <ZoneLocationForm
                    ref="locationForm"
                    :locations="bulkEditData.locations"
                    :clientId="clientId"
                    :disabled="isSaving"
                    @locationsChanged="onLocationsChanged"
                  />
                </v-flex>
              </v-flex>

              <!-- Selected Zones Summary -->
              <v-flex md12 class="mt-4">
                <v-layout align-center justify-space-between>
                  <h6 class="pr-3 pb-0">Selected Zones</h6>
                </v-layout>

                <v-flex md12 class="mt-2">
                  <v-card
                    flat
                    class="pa-2"
                    style="
                      border: 1px solid var(--background-color-400);
                      max-height: 200px;
                      overflow-y: auto;
                    "
                  >
                    <div
                      v-for="zone in selectedZonesSummary"
                      :key="zone.zoneId"
                      class="mb-1"
                    >
                      <strong>Zone {{ zone.zoneId }}:</strong>
                      {{ zone.zoneName }}
                      <span class="grey--text"
                        >({{ zone.locationCount }} locations)</span
                      >
                    </div>
                  </v-card>
                </v-flex>
              </v-flex>

              <!-- Action Buttons -->
              <v-flex md12 class="mt-4">
                <v-layout justify-end>
                  <v-btn
                    color="grey"
                    depressed
                    @click="closeDialog"
                    :disabled="isSaving"
                    class="mr-2"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    color="blue"
                    depressed
                    @click="onSave"
                    :loading="isSaving"
                    :disabled="!hasChanges"
                  >
                    Apply Changes
                  </v-btn>
                </v-layout>
              </v-flex>
            </v-layout>
          </div>
        </v-flex>
      </v-layout>
    </div>
  </ContentDialog>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { UpdateZoneRequest } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/UpdateZoneRequest';
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { useZoneToZoneStore } from '@/store/modules/ZoneToZoneStore';
import { computed, ref, watch, WritableComputedRef } from 'vue';
import ZoneLocationForm from './zone_location_form.vue';

interface Props {
  showDialog: boolean;
  selectedZones: string[];
  clientId: string;
}

interface BulkEditData {
  zoneName: string;
  locationAction: 'add' | 'remove' | 'none';
  locations: ZoneLocationSummary[];
}

interface ZoneSummary {
  zoneId: number;
  zoneName: string;
  locationCount: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'save'): void;
}>();

const zoneToZoneStore = useZoneToZoneStore();

// Reactive data
const isSaving = ref(false);
const bulkEditData = ref<BulkEditData>({
  zoneName: '',
  locationAction: 'none',
  locations: [],
});

// Mock data for selected zones summary (in real implementation, this would come from props or store)
const selectedZonesSummary = ref<ZoneSummary[]>([]);

// Dialog controller
const dialogController: WritableComputedRef<boolean> = computed({
  get() {
    return props.showDialog;
  },
  set(value: boolean) {
    if (!value) {
      emit('close');
    }
  },
});

// Computed properties
const hasChanges = computed(() => {
  return (
    bulkEditData.value.zoneName.trim() !== '' ||
    (bulkEditData.value.locationAction !== 'none' &&
      bulkEditData.value.locations.length > 0)
  );
});

// Watch for dialog opening
watch(
  () => props.showDialog,
  (isOpen) => {
    if (isOpen) {
      initializeBulkEditData();
      loadSelectedZonesSummary();
    }
  },
);

const initializeBulkEditData = () => {
  bulkEditData.value = {
    zoneName: '',
    locationAction: 'none',
    locations: [],
  };
};

const loadSelectedZonesSummary = async () => {
  // In a real implementation, you would extract zone IDs from selectedZones
  // and fetch their details from the store or API
  // For now, creating mock data
  selectedZonesSummary.value = props.selectedZones
    .filter((key) => key.startsWith('group-'))
    .map((key) => {
      const zoneId = parseInt(key.replace('group-', ''));
      return {
        zoneId,
        zoneName: `Zone ${zoneId}`,
        locationCount: 3, // Mock count
      };
    });
};

const onLocationsChanged = (locations: ZoneLocationSummary[]) => {
  bulkEditData.value.locations = locations;
};

const onSave = async () => {
  if (!hasChanges.value) {
    return;
  }

  isSaving.value = true;
  try {
    // Extract zone IDs from selected zones
    const zoneIds = props.selectedZones
      .filter((key) => key.startsWith('group-'))
      .map((key) => parseInt(key.replace('group-', '')));

    // Apply bulk changes to each zone
    const promises = zoneIds.map(async (zoneId) => {
      const request: UpdateZoneRequest = {
        clientId: props.clientId,
        zoneId,
        zoneName: bulkEditData.value.zoneName.trim() || '', // Empty string means no name change
        addedZoneLocations:
          bulkEditData.value.locationAction === 'add'
            ? bulkEditData.value.locations
            : [],
        deletedZoneLocations:
          bulkEditData.value.locationAction === 'remove'
            ? bulkEditData.value.locations
            : [],
      };

      return zoneToZoneStore.updateZone(request);
    });

    const results = await Promise.all(promises);

    // Check if all updates were successful
    if (results.every((result) => result !== null)) {
      emit('save');
    } else {
      console.error('Some bulk updates failed');
    }
  } catch (error) {
    console.error('Failed to perform bulk edit:', error);
  } finally {
    isSaving.value = false;
  }
};

const closeDialog = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
.dialog-content {
  min-height: 400px;
}

.body-scrollable--65 {
  max-height: 65vh;
  overflow-y: auto;
}
</style>
