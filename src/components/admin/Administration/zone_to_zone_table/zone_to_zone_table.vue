<template>
  <div class="zone-to-zone-table">
    <!-- Search and Actions Header -->
    <v-layout row wrap align-center class="mb-3">
      <v-flex md6>
        <v-text-field
          v-model="searchQuery"
          appendIcon="search"
          label="Search Zones"
          hint="Search by zone name, suburb, postcode, or state"
          color="orange"
          solo
          flat
          class="v-solo-custom"
          clearable
          @input="debouncedSearch"
        />
      </v-flex>
      <v-flex md6>
        <v-layout justify-end>
          <v-btn
            color="blue"
            depressed
            @click="openCreateDialog"
            :disabled="isLoading"
          >
            <v-icon left>add</v-icon>
            Create New Zone
          </v-btn>
        </v-layout>
      </v-flex>
    </v-layout>

    <!-- Table with grouped zones -->
    <v-layout class="zone-table-container">
      <div class="table-header-left-action-icon-container">
        <v-checkbox
          v-if="filteredGroupedZones.length > 0"
          @click.stop
          :indeterminate="zonesPartiallySelected"
          v-model="allZonesCheckBox"
          hide-details
          :ripple="false"
          :disabled="isLoading"
          color="info"
        />
      </div>

      <v-data-table
        :headers="tableHeaders"
        :items="tableItems"
        :loading="isLoading"
        :no-data-text="isLoading ? 'Loading zones...' : 'No zones found'"
        class="default-table-dark client-invoice-accounting-table gd-dark-theme"
        hide-actions
        :rows-per-page-items="[15, 25, 50]"
        item-key="uniqueKey"
      >
        <template v-slot:items="slotProps">
          <tr
            v-if="slotProps.item.isGroupHeader"
            class="zone-group-header"
            @click="toggleGroupExpansion(slotProps.item.groupId)"
          >
            <td class="inner-table__cell checkbox-type">
              <v-checkbox
                @click.stop
                v-model="slotProps.item.isSelected"
                hide-details
                :ripple="false"
                :disabled="isLoading"
                @change="onGroupSelectionChange(slotProps.item.groupId, $event)"
                color="info"
              />
            </td>
            <td class="zone-group-header-cell" colspan="4">
              <v-layout align-center>
                <v-icon class="mr-2">
                  {{
                    slotProps.item.isExpanded ? 'expand_less' : 'expand_more'
                  }}
                </v-icon>
                <strong
                  >Zone {{ slotProps.item.zoneId }}:
                  {{ slotProps.item.zoneName }}</strong
                >
                <v-spacer />
                <v-btn
                  icon
                  small
                  @click.stop="openEditDialog(slotProps.item.zoneData)"
                  :disabled="isLoading"
                >
                  <v-icon small>edit</v-icon>
                </v-btn>
              </v-layout>
            </td>
          </tr>

          <tr
            v-else
            class="zone-location-row"
            :class="{ 'zone-location-selected': slotProps.item.isSelected }"
          >
            <td class="inner-table__cell checkbox-type">
              <v-checkbox
                @click.stop
                v-model="slotProps.item.isSelected"
                hide-details
                :ripple="false"
                :disabled="isLoading"
                @change="onLocationSelectionChange(slotProps.item)"
                color="info"
              />
            </td>
            <td class="zone-location-indent">{{ slotProps.item.suburb }}</td>
            <td>{{ slotProps.item.postcode }}</td>
            <td>{{ slotProps.item.state }}</td>
            <td>
              <v-btn
                icon
                small
                @click="openEditLocationDialog(slotProps.item)"
                :disabled="isLoading"
              >
                <v-icon small>edit</v-icon>
              </v-btn>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-layout>

    <!-- Bulk Actions -->
    <!-- <v-layout v-if="selectedZones.length > 0" class="mt-3" justify-end>
      <v-btn
        color="warning"
        depressed
        @click="openBulkEditDialog"
        :disabled="isLoading"
      >
        <v-icon left>edit</v-icon>
        Bulk Edit ({{ selectedZones.length }})
      </v-btn>
    </v-layout> -->

    <!-- Create/Edit Zone Dialog -->
    <ZoneEditDialog
      :showDialog="showEditDialog"
      :isEditing="isEditing"
      :zoneData="editingZone"
      :clientId="clientId"
      @close="closeEditDialog"
      @save="onZoneSaved"
    />

    <!-- Bulk Edit Dialog -->
    <!-- <ZoneBulkEditDialog
      :showDialog="showBulkEditDialog"
      :selectedZones="selectedZones"
      :clientId="clientId"
      @close="closeBulkEditDialog"
      @save="onBulkEditSaved"
    /> -->

    <!-- Edit Location Dialog -->
    <ZoneLocationEditDialog
      :showDialog="showLocationEditDialog"
      :locationData="editingLocation"
      @close="closeLocationEditDialog"
      @save="onLocationSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { ZoneSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneSummary';
import { useZoneToZoneStore } from '@/store/modules/ZoneToZoneStore';
import { computed, onMounted, ref, watch, WritableComputedRef } from 'vue';
import ZoneEditDialog from './components/zone_edit_dialog.vue';
import ZoneLocationEditDialog from './components/zone_location_edit_dialog.vue';

interface Props {
  client?: { clientId: string } | null;
}

const props = withDefaults(defineProps<Props>(), {
  client: null,
});

const zoneToZoneStore = useZoneToZoneStore();

// Reactive data
const zones = ref<ZoneSummary[]>([]);
const isLoading = ref(false);
const searchQuery = ref('');
const expandedGroups = ref<Set<number>>(new Set());
const selectedZones = ref<string[]>([]);

// Dialog states
const showEditDialog = ref(false);
// const showBulkEditDialog = ref(false);
const showLocationEditDialog = ref(false);
const isEditing = ref(false);
const editingZone = ref<ZoneSummary | null>(null);
const editingLocation = ref<ZoneLocationSummary | null>(null);

// Computed properties
const clientId = computed(() => props.client?.clientId || '0');

const tableHeaders = computed(() => [
  { text: '', value: 'checkbox', sortable: false, width: '50px' },
  { text: 'Suburb', value: 'suburb', sortable: true },
  { text: 'Postcode', value: 'postcode', sortable: true },
  { text: 'State', value: 'state', sortable: true },
  { text: 'Actions', value: 'actions', sortable: false, width: '80px' },
]);

// Search functionality with debouncing
let searchTimeout: ReturnType<typeof setTimeout>;
const debouncedSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    // Search is handled in computed filteredGroupedZones
  }, 300);
};

// Filter zones based on search query
const filteredGroupedZones = computed(() => {
  if (!searchQuery.value.trim()) {
    return zones.value;
  }

  const searchTerm = searchQuery.value.toLowerCase().trim();
  return zones.value
    .filter((zone) => {
      // Search in zone name
      if (zone.zoneName.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // Search in zone locations
      return zone.zoneLocationsList.some(
        (location) =>
          location.suburb.toLowerCase().includes(searchTerm) ||
          location.postcode.toLowerCase().includes(searchTerm) ||
          location.state.toLowerCase().includes(searchTerm),
      );
    })
    .map((zone) => {
      // If searching, expand groups that contain matching items
      if (
        searchTerm &&
        zone.zoneLocationsList.some(
          (location) =>
            location.suburb.toLowerCase().includes(searchTerm) ||
            location.postcode.toLowerCase().includes(searchTerm) ||
            location.state.toLowerCase().includes(searchTerm),
        )
      ) {
        expandedGroups.value.add(zone.zoneId);
      }
      return zone;
    });
});

// Transform zones into table items with grouping
const tableItems = computed(() => {
  const items: any[] = [];

  filteredGroupedZones.value.forEach((zone) => {
    const groupId = zone.zoneId;
    const isExpanded = expandedGroups.value.has(groupId);

    // Add group header
    items.push({
      uniqueKey: `group-${groupId}`,
      isGroupHeader: true,
      groupId,
      zoneId: zone.zoneId,
      zoneName: zone.zoneName,
      zoneData: zone,
      isExpanded,
      isSelected: selectedZones.value.includes(`group-${groupId}`),
    });

    // Add location rows if expanded
    if (isExpanded) {
      zone.zoneLocationsList.forEach((location, index) => {
        items.push({
          uniqueKey: `location-${groupId}-${index}`,
          isGroupHeader: false,
          groupId,
          ...location,
          isSelected: selectedZones.value.includes(
            `location-${groupId}-${index}`,
          ),
        });
      });
    }
  });

  return items;
});

// Selection logic
const zonesPartiallySelected = computed(() => {
  const totalSelectableItems = tableItems.value.length;
  const selectedCount = selectedZones.value.length;
  return selectedCount > 0 && selectedCount < totalSelectableItems;
});

const allZonesCheckBox: WritableComputedRef<boolean> = computed({
  get() {
    const totalSelectableItems = tableItems.value.length;
    return (
      totalSelectableItems > 0 &&
      selectedZones.value.length === totalSelectableItems
    );
  },
  set(value: boolean) {
    if (value) {
      selectedZones.value = tableItems.value.map((item) => item.uniqueKey);
    } else {
      selectedZones.value = [];
    }
  },
});

// Methods
const loadZones = async () => {
  isLoading.value = true;
  try {
    const result = await zoneToZoneStore.requestZoneListForClient(
      clientId.value,
    );
    zones.value = result || [];
  } catch (error) {
    console.error('Failed to load zones:', error);
    zones.value = [];
  } finally {
    isLoading.value = false;
  }
};

const toggleGroupExpansion = (groupId: number) => {
  if (expandedGroups.value.has(groupId)) {
    expandedGroups.value.delete(groupId);
  } else {
    expandedGroups.value.add(groupId);
  }
};

const onGroupSelectionChange = (groupId: number, selected: boolean) => {
  const groupKey = `group-${groupId}`;
  const locationKeys = tableItems.value
    .filter((item) => !item.isGroupHeader && item.groupId === groupId)
    .map((item) => item.uniqueKey);

  if (selected) {
    selectedZones.value = [
      ...new Set([...selectedZones.value, groupKey, ...locationKeys]),
    ];
  } else {
    selectedZones.value = selectedZones.value.filter(
      (key) => key !== groupKey && !locationKeys.includes(key),
    );
  }
};

const onLocationSelectionChange = (item: any) => {
  const locationKey = item.uniqueKey;
  const groupKey = `group-${item.groupId}`;

  if (item.isSelected) {
    selectedZones.value = [...new Set([...selectedZones.value, locationKey])];
  } else {
    selectedZones.value = selectedZones.value.filter(
      (key) => key !== locationKey && key !== groupKey,
    );
  }
};

// Dialog methods
const openCreateDialog = () => {
  isEditing.value = false;
  editingZone.value = null;
  showEditDialog.value = true;
};

const openEditDialog = (zone: ZoneSummary) => {
  isEditing.value = true;
  editingZone.value = zone;
  showEditDialog.value = true;
};

const closeEditDialog = () => {
  showEditDialog.value = false;
  editingZone.value = null;
};

// const openBulkEditDialog = () => {
//   showBulkEditDialog.value = true;
// };

// const closeBulkEditDialog = () => {
//   showBulkEditDialog.value = false;
// };

const openEditLocationDialog = (location: ZoneLocationSummary) => {
  editingLocation.value = location;
  showLocationEditDialog.value = true;
};

const closeLocationEditDialog = () => {
  showLocationEditDialog.value = false;
  editingLocation.value = null;
};

// Save handlers
const onZoneSaved = () => {
  closeEditDialog();
  loadZones();
};

// const onBulkEditSaved = () => {
//   closeBulkEditDialog();
//   selectedZones.value = [];
//   loadZones();
// };

const onLocationSaved = () => {
  closeLocationEditDialog();
  loadZones();
};

// Lifecycle
onMounted(() => {
  loadZones();
});

// Watch for client changes
watch(
  () => props.client?.clientId,
  () => {
    if (props.client?.clientId) {
      loadZones();
    }
  },
);
</script>

<style lang="scss" scoped>
.zone-to-zone-table {
  width: 100%;
}

.zone-table-container {
  position: relative;
}

.table-header-left-action-icon-container {
  position: absolute;
  top: -3px;
  left: 13px;
  z-index: 5;
}

.zone-group-header {
  background-color: var(--background-color-300);
  cursor: pointer;

  &:hover {
    background-color: var(--background-color-250);
  }
}

.zone-group-header-cell {
  font-weight: 600;
}

.zone-location-row {
  &.zone-location-selected {
    background-color: var(--background-color-200);
  }
}

.zone-location-indent {
  padding-left: 32px !important;
}

.inner-table__cell {
  cursor: pointer;
  padding: 0 4px !important;

  &.checkbox-type {
    padding-left: 12px !important;
  }
}
</style>
