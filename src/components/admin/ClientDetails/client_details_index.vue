<template>
  <v-layout wrap v-if="clientDetails">
    <v-flex md12>
      <v-layout
        justify-space-between
        v-if="isDialog"
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span><span>Client Details - </span>{{ clientName }}</span>

        <div
          class="app-theme__center-content--closebutton"
          :class="{ 'disable-pointer-events': isEdited }"
          @click="closeClientDialog"
        >
          <v-icon
            :disabled="isEdited"
            class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <v-layout
        class="app-theme__center-content--body dialog-content"
        row
        wrap
        :class="isDialog ? 'main-content-dialog' : 'main-content-route'"
      >
        <v-flex
          lg3
          md4
          class="side-column app-bgcolor--400 app-bordercolor--600 app-borderside--r-shadow"
        >
          <v-layout row wrap>
            <v-flex
              md12
              class="app-bgcolor--250 app-bordercolor--600 app-borderside--b"
            >
              <v-layout row wrap py-2 px-2>
                <v-flex md12>
                  <v-form>
                    <SelectEntity
                      :key="clientDetails.clientId"
                      :disabled="isEdited || !clientDetails._id"
                      :entityTypes="[entityType.CLIENT]"
                      :isRouteSelect="true"
                    />
                  </v-form>
                </v-flex>

                <v-flex md12 class="side-column__summaryitem">
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key"> Status </span>
                    <v-layout
                      v-for="(value, index) in currentStatus"
                      :key="index"
                      class="d-flex justify-end"
                    >
                      <v-flex class="status-badge" :class="value.color">
                        {{ value.statusName }}
                      </v-flex>
                    </v-layout>
                  </v-layout>
                </v-flex>

                <v-flex
                  md12
                  class="side-column__summaryitem"
                  v-for="infoItem in summaryInfoList"
                  :key="infoItem.id"
                >
                  <v-layout
                    v-if="summaryInfoList"
                    justify-space-between
                    align-start
                  >
                    <span class="side-column__summaryitem--key">
                      {{ infoItem.title }}
                    </span>
                    <span class="side-column__summaryitem--value">
                      {{ infoItem.value }}
                    </span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex
              md12
              v-for="(menuOption, optionIndex) in menuOptions"
              :key="menuOption.id"
              :pt-1="optionIndex === 0"
            >
              <v-layout row wrap>
                <v-flex md12 class="app-bordercolor--600 app-borderside--b">
                  <v-layout>
                    <h5 class="subheader--bold--12 px-3 pt-2 pb-1">
                      {{ menuOption.title }}
                    </h5>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  v-for="menuItem in filteredMenuItems(menuOption.items)"
                  :key="menuItem.id"
                  class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
                  :class="[
                    selectedViewType === menuItem.id ? 'active-state' : '',
                    menuItem.isActive
                      ? 'menu-item-selectable'
                      : 'menu-item-disabled',
                  ]"
                  @click="setSelectedView(menuItem.id)"
                >
                  <v-layout align-center>
                    <span class="button-label"
                      ><span class="pr-2">-</span>{{ menuItem.title }}</span
                    >
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex
              md12
              v-if="isAuthorisedToViewLedger()"
              class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
              :class="[
                !isEdited ? 'menu-item-selectable' : 'menu-item-disabled',
                !isAuthorisedToViewLedger() ? 'disable-pointer-events' : '',
              ]"
              @click="setInvoicingSearchDialog(true)"
            >
              <v-layout justify-space-between align-center
                ><span class="button-label"
                  ><span class="pr-2">-</span>INVOICE HISTORY</span
                >
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex
          lg9
          md8
          class="dialog-content__scrollable"
          style="position: initial"
        >
          <v-layout
            class="dialog-toolbar center-section__top-toolbar"
            style="position: relative"
            align-center
          >
            <v-btn
              depressed
              v-if="isEdited"
              @click="findAndSetSelectedClient"
              outline
              color="error"
              small
            >
              Cancel
            </v-btn>

            <v-btn
              depressed
              v-if="!isEdited && !isDialog"
              @click="exitClient"
              outline
              color="error"
              small
            >
              exit
            </v-btn>
            <v-spacer />
            <v-btn
              :disabled="!isAuthorised()"
              depressed
              color="blue"
              small
              v-if="!isEdited && selectedView?.isEditable"
              @click="setEdited(true)"
            >
              {{ selectedView.actionBtnText }}
            </v-btn>
            <v-btn
              v-if="isEdited && selectedViewType !== 'NOT'"
              depressed
              color="blue"
              :disabled="awaitingClientDetailsSaveResponse"
              :loading="awaitingClientDetailsSaveResponse"
              small
              @click="saveClientDetails"
            >
              Save
            </v-btn>
          </v-layout>

          <v-layout
            class="scrollable"
            :key="clientDetails ? clientDetails.clientId : 'new'"
          >
            <v-form
              ref="clientForm"
              style="width: 100%"
              v-if="selectedView?.isForm"
            >
              <v-layout fill-height>
                <v-flex lg8 md10 offset-lg2 offset-md1 class="pt-4">
                  <ClientDetailsKeyInformation
                    v-if="selectedViewType === 'KEY'"
                    :isEdited="isEdited"
                    :clientDetails="clientDetails"
                  ></ClientDetailsKeyInformation>
                  <ClientDetailsCompanyDetails
                    v-if="selectedViewType === 'COM'"
                    :isEdited="isEdited"
                    :clientDetails="clientDetails"
                  ></ClientDetailsCompanyDetails>
                  <ClientDetailsOperationalInformation
                    v-if="selectedViewType === 'OPS'"
                    :isEdited="isEdited"
                    :clientDetails="clientDetails"
                  ></ClientDetailsOperationalInformation>

                  <ClientDetailsSharedEmails
                    v-if="selectedViewType === 'SHE'"
                    :isEdited="isEdited"
                    :clientDetails="clientDetails"
                  ></ClientDetailsSharedEmails>
                  <ClientDetailsAccountsPayable
                    v-if="selectedViewType === 'ACP'"
                    :isEdited="isEdited"
                    :clientDetails="clientDetails"
                  ></ClientDetailsAccountsPayable>
                </v-flex>
              </v-layout>
            </v-form>
            <v-flex md12 v-if="!selectedView?.isForm" class="pa-3">
              <ClientDetailsOperationalReferences
                v-if="selectedViewType === 'ORF'"
                :clientDetails="clientDetails"
                @saveParentDocument="saveClientDetails"
                :isLoading="awaitingClientDetailsSaveResponse"
              ></ClientDetailsOperationalReferences>

              <ClientDetailsCommonAddressManagement
                :clientId="clientDetails.clientId"
                :client_id="clientDetails._id"
                :clientPersonIds="clientDetails.clientPersonDispatchers"
                v-if="selectedViewType === 'ADD'"
                :isDialogOpen="selectedViewType === 'ADD'"
              />
              <ClientDetailsClientContacts
                :clientId="clientDetails.clientId"
                :client_id="clientDetails._id"
                :clientName="clientName"
                :clientPersonIds="clientDetails.clientPersonDispatchers"
                :defaultDispatcherId="clientDetails.defaultDispatcherId"
                @addClientPersonDispatcherId="addClientPersonDispatcherId"
                v-if="selectedViewType === 'PER'"
              />
              <ClientDetailsNotes
                v-if="selectedViewType === 'NOT'"
                :notes="clientDetails.specialInstructions"
                :isLoading="awaitingClientDetailsSaveResponse"
                :isEdited="isEdited"
                @setEdited="setEdited"
                @saveParentDocument="saveClientDetails"
              />
              <!-- <FuelSurchargeLevyDetails
                v-if="selectedViewType === 'FUEL' && clientDetails.clientId"
                :config="{
                  type: RateEntityType.CLIENT,
                  clientId: clientDetails.clientId,
                  fuelSurchargeRates: allFuelSurchargeList,
                }"
                :activeFuelSurchargeId="activeFuelSurchargeId"
                @refreshServiceRateList="refreshServiceRateList"
              > -->
              <FuelSurchargeLevyDetails
                v-if="selectedViewType === 'FUEL' && clientDetails.clientId"
                :componentType="FuelComponentType.CLIENT"
                key="clientFuelSurcharge"
                :entityId="clientDetails.clientId"
              >
                <ActiveRatesSummary
                  slot="active-rates-summary"
                  serviceRateType="CLIENT"
                  :clientDetails="clientDetails"
                  :currentServiceRate="currentActiveServiceRate"
                  :currentFuelSurcharge="activeFuelSurchargeRate"
                ></ActiveRatesSummary>
              </FuelSurchargeLevyDetails>
              <ServiceRateAdministration
                v-if="selectedViewType === 'SERV'"
                serviceRateType="CLIENT"
                :clientDetails="clientDetails"
                :currentActiveClientServiceRate="currentActiveServiceRate"
                :currentDivisionServiceRate="divisionServiceRate"
                :allClientServiceRates="allClientServiceRates"
                :hasServiceRateTable="hasServiceRateTable"
                :isEdited="isEdited"
                :allFuelSurchargeRates="allFuelSurchargeList"
                :activeFuelSurchargeId="activeFuelSurchargeId"
                @refreshServiceRateList="refreshServiceRateList"
              ></ServiceRateAdministration>
              <DefaultRatesConfiguration
                v-if="selectedViewType === 'DEF' && divisionServiceRate"
                :entityDetails="clientDetails"
                :defaultServiceRate="divisionServiceRate"
                :serviceRateType="RateEntityType.CLIENT"
                :allDefaultRates="divisionRates"
              >
                <ActiveRatesSummary
                  slot="active-rates-summary"
                  serviceRateType="CLIENT"
                  :clientDetails="clientDetails"
                  :currentServiceRate="currentActiveServiceRate"
                  :currentFuelSurcharge="activeFuelSurchargeRate"
                ></ActiveRatesSummary>
              </DefaultRatesConfiguration>

              <ClientDetailsRateConfiguration
                v-if="selectedViewType === 'RCONFIG'"
                :isEdited="isEdited"
                :clientDetails="clientDetails"
                :allClientServiceRates="allClientServiceRates"
                :allFuelSurchargeRates="allFuelSurchargeList"
                @updateFuelSurchargeConfig="handleFuelSurchargeConfigUpdate"
                @updateServiceRateConfig="handleServiceRateConfigUpdate"
                @updateDivisionRatesConfig="handleDivisionRatesConfigUpdate"
              />

              <ClientDetailsActiveAssociations
                v-if="selectedViewType === 'ACA'"
                :clientDetails="clientDetails"
              />

              <ClientDetailsIntegrations
                :isEdited="isEdited"
                v-if="selectedViewType === 'INT'"
                :clientDetails="clientDetails"
                @exitClient="exitClient"
              />

              <ServiceRateVariations
                v-if="selectedViewType === 'SRVAR'"
                :clientId="clientDetails.clientId"
              />

              <ZoneToZoneTable
                v-if="selectedViewType === 'ZTZA'"
                :clientId="clientDetails.clientId"
              />

              <ClientDetailsRateSummaryTable
                v-if="selectedViewType === 'CRS' && clientDetails.clientId"
                :serviceRate="allClientServiceRates"
                :clientId="clientDetails.clientId"
                :clientRates="clientRates"
                :divisionRates="divisionRates"
              />
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-dialog
      v-model="documentUpdatedDialogIsOpen"
      :width="400"
      class="ma-0"
      content-class="v-dialog-custom"
    >
      <v-card color="#242329">
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Document Update Alert</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="closeDocumentUpdatedDialog"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout row wrap class="px-4 py-4">
          The document you are viewing was recently updated by another user. To
          minimise conflicts we require the document to be refreshed.
        </v-layout>
        <v-divider class="mt-2"></v-divider>
        <v-layout justify-end>
          <v-btn
            depressed
            color="blue"
            @click="closeDocumentUpdatedDialog"
            class="v-btn-confirm-custom"
            >Okay
          </v-btn>
        </v-layout>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="isViewingInvoiceSearchDialog"
      v-if="isViewingInvoiceSearchDialog"
      width="85vw"
      :transition="false"
      content-class="v-dialog-custom"
      persistent
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Invoice Search</span>

        <div
          class="app-theme__center-content--closebutton"
          @click="isViewingInvoiceSearchDialog = false"
        >
          <v-icon size="14" class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout
        class="app-theme__center-content--body pa-3 invoice-search-dialog"
        style="max-height: 90vh"
      >
        <SearchLedger :isClient="true" :clientId="clientDetails.clientId" />
      </v-layout>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import SearchLedger from '@/components/admin/Accounting/ledger/components/search_ledger/index.vue';
import ZoneToZoneTable from '@/components/admin/Administration/zone_to_zone_table/index.vue';
import ClientDetailsAccountsPayable from '@/components/admin/ClientDetails/components/client_details_accounts_payable.vue';
import ClientDetailsActiveAssociations from '@/components/admin/ClientDetails/components/client_details_active_associations.vue';
import ClientDetailsCommonAddressManagement from '@/components/admin/ClientDetails/components/client_details_common_address/client_details_common_address_management.vue';
import ClientDetailsCompanyDetails from '@/components/admin/ClientDetails/components/client_details_company_details.vue';
import ClientDetailsClientContacts from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_client_contacts_management.vue';
import ClientDetailsIntegrations from '@/components/admin/ClientDetails/components/client_details_integrations.vue';
import ClientDetailsKeyInformation from '@/components/admin/ClientDetails/components/client_details_key_information.vue';
import ClientDetailsNotes from '@/components/admin/ClientDetails/components/client_details_notes.vue';
import ClientDetailsOperationalInformation from '@/components/admin/ClientDetails/components/client_details_operational_information.vue';
import ClientDetailsOperationalReferences from '@/components/admin/ClientDetails/components/client_details_operational_references.vue';
import ClientDetailsRateConfiguration from '@/components/admin/ClientDetails/components/client_details_rate_configuration.vue';
import ClientDetailsRateSummaryTable from '@/components/admin/ClientDetails/components/client_details_rate_summary/client_details_rate_summary_table.vue';
import ClientDetailsSharedEmails from '@/components/admin/ClientDetails/components/client_details_shared_emails.vue';
import ServiceRateVariations from '@/components/admin/ClientDetails/components/client_rate_variations_table.vue';
import FuelSurchargeLevyDetails from '@/components/common/fuel_surcharge_levy_details/fuel_surcharge_levy_details.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import ActiveRatesSummary from '@/components/common/service-rate-table/active_rates_summary/index.vue';
import DefaultRatesConfiguration from '@/components/common/service-rate-table/default-rates-configuration/default_rates_configuration.vue';
import ServiceRateAdministration from '@/components/common/service-rate-table/service_rate_administration/index.vue';
import { returnTimeNow } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnCreditStatusFromId,
  returnTradingTermsFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import {
  hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole,
  hasAdminOrHeadOfficeRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { initialiseClientDetails } from '@/helpers/classInitialisers/InitialiseClientDetails';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import ClientIntegrationUpdate from '@/interface-models/Client/ClientImportUserUpdate';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { KeyValuePair } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ClientFuelSurchargeRate } from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { FuelComponentType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelComponentType';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import {
  ClientPersonWithAuthDetails,
  SaveNewClientPersonResponse,
} from '@/interface-models/User/ClientPerson';
import { UpdateRoleAccessResponse } from '@/interface-models/User/UpdateRoleAccess';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { useMittListener } from '@/utils/useMittListener';
import {
  ComputedRef,
  Ref,
  computed,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

interface StatusConfig {
  statusName: string;
  color: string;
}
interface MenuCategory {
  id: string;
  title?: string;
  items: ClientMenu[];
}

interface ClientMenu {
  id: string;
  title: string;
  isForm: boolean;
  isActive: boolean;
  isEditable: boolean;
  actionBtnText: string;
  isHidden?: boolean;
}

interface ClientRateData {
  allServiceRates: boolean;
  currentServiceRate: boolean;
  allFuelSurcharges: boolean;
  activeFuelSurcharge: boolean;
  divisionServiceRates: boolean;
}

const props = withDefaults(
  defineProps<{
    isDialog?: boolean;
  }>(),
  {
    isDialog: false,
  },
);

const componentTitle: string = 'Client Details Administration';

const serviceRateStore = useServiceRateStore();
const fuelLevyStore = useFuelLevyStore();
const clientDetailsStore = useClientDetailsStore();
const userManagementStore = useUserManagementStore();

const entityType = ref(EntityType);

const selectedViewType: Ref<string> = ref('KEY');

const documentUpdatedDialogIsOpen: Ref<boolean> = ref(false);
const isEdited: Ref<boolean> = ref(false);
const awaitingClientDetailsSaveResponse: Ref<boolean> = ref(false);
const isViewingInvoiceSearchDialog: Ref<boolean> = ref(false);
const hasServiceRateTable: Ref<boolean> = ref(false);
const isExpiredFuelSurcharge: Ref<boolean> = ref(false);

const clientForm: Ref<any> = ref(null);
const clientDetails: Ref<ClientDetails | null> = ref(null);

const clientRates: Ref<ClientServiceRate[] | null> = ref([]);
const divisionRates: Ref<ClientServiceRate[] | null> = ref([]);

const router = useRouter();
const route = useRoute();
const routeId: ComputedRef<string> = computed(() => route.params.id);

const clientRateData: ClientRateData = {
  allFuelSurcharges: false,
  currentServiceRate: false,
  allServiceRates: false,
  activeFuelSurcharge: false,
  divisionServiceRates: false,
};

const filteredMenuItems = (items: ClientMenu[]) => {
  return items.filter((item) => !item.isHidden);
};

const divisionServiceRate: Ref<ClientServiceRate | null> = ref(null);
const currentActiveServiceRate: Ref<ClientServiceRate | null> = ref(null);
const allClientServiceRates: Ref<ClientServiceRate[]> = ref([]);
const allFuelSurchargeList: Ref<ClientFuelSurchargeRate[]> = ref([]);
const activeFuelSurchargeId: Ref<string | null> = ref(null);

const isAuthorised = (): boolean => {
  return hasAdminOrHeadOfficeRole();
};
const isAuthorisedToViewLedger = (): boolean => {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
};

const selectedClientId: ComputedRef<string | null> = computed(() => {
  return clientDetailsStore.selectedClientId;
});

watch(selectedClientId, () => {
  findAndSetSelectedClient();
});

const isNewClientDetails: ComputedRef<boolean> = computed(() => {
  return routeId.value === 'new' || selectedClientId.value === 'new';
});

async function findAndSetSelectedClient(
  isCancelNewClient: boolean = false,
): Promise<void> {
  if (!props.isDialog && selectedClientId.value !== routeId.value) {
    clientDetailsStore.setClientPersons([]);
    clientDetailsStore.setSelectedClientId(routeId.value);
  }

  isEdited.value = false;
  if (isCancelNewClient && isNewClientDetails.value) {
    if (!props.isDialog) {
      exitClient();
    } else {
      closeClientDialog();
    }
    return;
  }

  // reset any validation issues on inputs.
  if (clientForm.value) {
    clientForm.value.resetValidation();
  }

  if (isNewClientDetails.value) {
    clientDetails.value = initialiseClientDetails(new ClientDetails());
    isEdited.value = true;
    return;
  }

  // Confirm that the selected client is known locally.
  const clientDetailsSummary: ClientSearchSummary | undefined =
    clientDetailsStore.clientSummaryList.find(
      (x: ClientSearchSummary) => x.clientId === selectedClientId.value,
    );

  if (!clientDetailsSummary) {
    handleUnknownClient();
    return;
  }

  // Request full ClientDetails object. Rates will be requested in the handler of the client details response.
  if (selectedClientId.value) {
    const clientDetails =
      await clientDetailsStore.requestClientDetailsByClientId(
        selectedClientId.value,
      );
    // Handle response
    if (clientDetails) {
      setClientDetailsFromResponse(clientDetails);
    } else {
      handleUnknownClient();
    }
  }
}

// Handle emit from Service Rate Index component to force refresh of rates
// table
const refreshServiceRateList = () => {
  if (clientDetailsStore.selectedClientId) {
    let clientId = clientDetailsStore.selectedClientId;
    if (
      clientId === 'new' &&
      clientDetails.value &&
      clientDetails.value.clientId
    ) {
      clientId = clientDetails.value.clientId;
    }
    getClientRates(clientId);
  }
};

// Set awaiting variables to false and request all service rates and fuel
// surcharges
async function getClientRates(clientId: string) {
  clientRateData.activeFuelSurcharge = false;
  clientRateData.allFuelSurcharges = false;
  clientRateData.allServiceRates = false;
  clientRateData.currentServiceRate = false;
  clientRateData.divisionServiceRates = false;

  // Send requests for all rates and fuel surcharges
  const [
    currentRateCard,
    currentDivisionRates,
    // allDivisionRates,
    allRates,
    currentFuelLevy,
    allFuel,
  ] = await Promise.all([
    serviceRateStore.getCurrentClientServiceRates(clientId),
    serviceRateStore.getCurrentDivisionDefaultRates(),
    // serviceRateStore.getAllDivisionDefaultServiceRates(),
    serviceRateStore.getAllServiceRatesForClientId(clientId),
    fuelLevyStore.getCurrentClientFuelSurcharges(clientId, returnTimeNow()),
    fuelLevyStore.getClientFuelSurchargeList(clientId),
  ]);

  // Handle current rate card response
  currentActiveServiceRate.value = currentRateCard?.clientServiceRate ?? null;
  hasServiceRateTable.value = currentActiveServiceRate.value !== null;
  clientRateData.currentServiceRate = true;

  // Handle division rate card response
  divisionServiceRate.value = currentDivisionRates?.clientServiceRate ?? null;
  clientRateData.divisionServiceRates = true;

  clientRates.value = allRates;
  // divisionRates.value = allDivisionRates;

  divisionRates.value =
    await serviceRateStore.getAllDivisionDefaultServiceRates();

  // Handle all rates response
  setClientServiceRateList(allRates ?? []);
  // Handle fuel responses
  setCurrentClientFuelSurcharge(currentFuelLevy);
  setAllClientFuelSurchargeRates(allFuel ?? []);
}

// Handles response of full ClientDetails after selection of a clientId
function setClientDetailsFromResponse(
  clientDetailsData: ClientDetails | null,
): void {
  if (!clientDetailsData || !clientDetailsData.clientId) {
    handleUnknownClient();
    return;
  }
  clientDetails.value = initialiseClientDetails(clientDetailsData);
  // Request the clients rates.
  getClientRates(clientDetailsData.clientId);
}

// If the selected client was not found, we should alert and push the user back to the client index page.
const handleUnknownClient = () => {
  showAppNotification('Sorry, we could not find that Client.');
  clientDetails.value = null;
  clientDetailsStore.setSelectedClientId(null);
  closeClientDialog();
  router.push({ name: 'Client' });
};

const clientName: ComputedRef<string> = computed(() => {
  if (!clientDetails.value) {
    return '';
  }
  return clientDetails.value.tradingName || clientDetails.value.clientName;
});

function closeClientDialog(): void {
  clientDetailsStore.setSelectedClientId('');
}

const menuOptions: ComputedRef<MenuCategory[]> = computed(() => {
  const basicInfo = {
    id: 'INFO',
    title: 'Client Info',
    items: [
      {
        id: 'KEY',
        title: 'KEY DETAILS',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },

      {
        id: 'COM',
        title: 'COMPANY DETAILS',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'ACA',
        title: 'CURRENT WORK',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
    ],
  };
  const operationInfo = {
    id: 'OPERATIONS',
    title: 'Operations',
    items: [
      {
        id: 'OPS',
        title: 'Operational Information',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'ORF',
        title: 'References',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },

      {
        id: 'NOT',
        title: 'NOTES',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Add Note',
      },
      {
        id: 'INT',
        title: 'ELECTRONIC DATA INTERCHANGE',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
    ],
  };
  const contactInfo = {
    id: 'CONTACT',
    title: 'Contact Info',
    items: [
      {
        id: 'PER',
        title: 'Client Contacts',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
      {
        id: 'ADD',
        title: 'Common Addresses',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
      {
        id: 'SHE',
        title: 'Email Settings',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
    ],
  };

  const rateInfo = {
    id: 'RATES',
    title: 'Rate Details',
    items: [
      {
        id: 'CRS',
        title: 'Client Rate Summary',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
      {
        id: 'RCONFIG',
        title: 'Rate Configurations',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'SERV',
        title: 'Client Service Rates',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
      {
        id: 'DEF',
        title: 'Division Service Rates',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
      {
        id: 'FUEL',
        title: 'Fuel Surcharge',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
      {
        id: 'SRVAR',
        title: 'Service Rate Variations',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
      {
        id: 'ZTZA',
        title: 'Zone Rate Administration',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
    ],
  };
  const otherInfo = {
    id: 'OTHER',
    title: 'Billing / Charge Details',
    items: [
      {
        id: 'ACP',
        title: 'Accounts Payable',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
    ],
  };

  return [basicInfo, operationInfo, contactInfo, rateInfo, otherInfo];
});

// Controls visibility of Invoicing Dialog component
function setInvoicingSearchDialog(value: boolean): void {
  if (!isAuthorisedToViewLedger()) {
    return;
  }
  isViewingInvoiceSearchDialog.value = value;
}

// Set the selected view from the left navigation menu
function setSelectedView(id: string): void {
  if (!id) {
    selectedViewType.value = menuOptions.value[0].items[0].id;
    return;
  }
  selectedViewType.value = id;
}

const selectedView: ComputedRef<ClientMenu | null> = computed(() => {
  const selectedView = menuOptions.value
    .flatMap((x) => x.items)
    .find((x) => x.id === selectedViewType.value);
  return selectedView ? selectedView : null;
});

// Data List to be displayed in the top left of
const summaryInfoList: ComputedRef<KeyValuePair[]> = computed(() => {
  const infoList: KeyValuePair[] = [];
  const details = clientDetails.value;
  if (!details) {
    return infoList;
  }
  infoList.push({
    id: 'clientId',
    title: 'Company ID',
    value: details.clientId ? details.clientId : '-',
  });
  infoList.push({
    id: 'companyName',
    title: 'Company Name',
    value: details.clientName ? details.clientName : '-',
  });
  infoList.push({
    id: 'tradingName',
    title: 'Trading Name',
    value: details.tradingName ? details.tradingName : '-',
  });
  const nationalClientName = details.nationalClientName;
  if (nationalClientName) {
    infoList.push({
      id: 'nationalClientName',
      title: 'National Client Name',
      value: nationalClientName,
    });
  }
  infoList.push({
    id: 'tradingTerms',
    title: 'Trading Terms',
    value: details.clientTradingTerms
      ? returnTradingTermsFromId(details.clientTradingTerms)
      : '-',
  });
  infoList.push({
    id: 'creditStatus',
    title: 'Credit Status',
    value: details.accountsReceivable?.creditStatus
      ? returnCreditStatusFromId(details.accountsReceivable.creditStatus)
      : '-',
  });
  return infoList;
});

// Return details of current status to be used as label in html
const currentStatus: ComputedRef<StatusConfig[]> = computed(() => {
  let statusConfig = [
    {
      statusName: '',
      color: '',
    },
  ];
  const details = clientDetails.value;
  if (!details) {
    return statusConfig;
  }

  const isRetired: boolean = details.statusList.includes(13);
  const isPlaceholderClient: boolean =
    !details._id && details.statusList.includes(3);
  const isPending: boolean = !!details._id && details.statusList.includes(3);
  const isSeeAccounts: boolean = details.statusList.includes(7);

  if (isSeeAccounts) {
    statusConfig.push({
      statusName: 'SEE ACCOUNTS',
      color: 'red',
    });
  }
  if (isPending) {
    statusConfig.push({
      statusName: 'PENDING (INCOMPLETE)',
      color: 'warning',
    });
  }
  if (isPlaceholderClient) {
    statusConfig.push({
      statusName: 'PLACEHOLDER',
      color: 'warning',
    });
  }
  if (isRetired) {
    statusConfig = [
      {
        statusName: 'RETIRED',
        color: 'grey',
      },
    ];
  }
  if (!details.statusList.length) {
    statusConfig = [
      {
        statusName: 'ACTIVE',
        color: 'success',
      },
    ];
  }

  return statusConfig;
});

// Validate form and send save request
async function saveClientDetails(): Promise<void> {
  if (!clientDetails.value) {
    return;
  }
  if (clientForm.value && !clientForm.value.validate()) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  awaitingClientDetailsSaveResponse.value = true;
  // Handle logic for when a pending client is saved with all minimum
  // requirements. (set them to active if inactive status is not found)
  const isPlaceholderClient: boolean =
    !clientDetails.value._id && clientDetails.value.statusList.includes(3);
  const isPending: boolean =
    !!clientDetails.value._id && clientDetails.value.statusList.includes(3);
  if (isPending && !isPlaceholderClient) {
    const pendingIndex = clientDetails.value.statusList.findIndex(
      (x: number) => x === 3,
    );
    clientDetails.value.statusList.splice(pendingIndex, 1);
  }
  const result = await clientDetails.value.save();
  setSavedClient(result);
}

// The current active ClientFuelSurchargeRate as defined by the
// activeFuelSurchargeId. Passed into the ActiveRatesSummary component
const activeFuelSurchargeRate: ComputedRef<ClientFuelSurchargeRate | null> =
  computed(() => {
    const activeId = activeFuelSurchargeId.value;
    if (activeId === null) {
      return null;
    }
    const foundActive = allFuelSurchargeList.value.find(
      (r) => r.id === activeId,
    );
    return foundActive ? foundActive : null;
  });

function setEdited(edited: boolean): void {
  isEdited.value = edited;
}

// Handles response for clientDetails save request
function setSavedClient(clientDetailsData: ClientDetails | null): void {
  if (!awaitingClientDetailsSaveResponse.value) {
    return;
  }

  if (!clientDetailsData) {
    showAppNotification(GENERIC_ERROR_MESSAGE);
    awaitingClientDetailsSaveResponse.value = false;
    return;
  }

  clientDetails.value = initialiseClientDetails(clientDetailsData);
  isEdited.value = false;

  showAppNotification(
    `${clientName.value} successfully saved.`,
    HealthLevel.INFO,
  );

  if (
    clientDetails.value.clientId &&
    selectedClientId.value !== clientDetails.value.clientId
  ) {
    clientDetailsStore.setSelectedClientId(clientDetails.value.clientId);
  }

  handleClientSaveSuccess();
  awaitingClientDetailsSaveResponse.value = false;
}

// if a new client was just saved we need to update the current route to reflect the new driver. We update the search selection as well to match the new driver.
const handleClientSaveSuccess = () => {
  if (!clientDetails.value || !clientDetails.value.clientId) {
    return;
  }
  if (!props.isDialog) {
    if (
      routeId.value !== clientName.value.toLowerCase().replace(/ /g, '-') &&
      routeId.value !== clientDetails.value.clientId
    ) {
      router.push({
        name: 'Client Details',
        params: {
          name: clientName.value.toLowerCase().replace(/ /g, '-'),
          id: clientDetails.value.clientId,
        },
      });
    }
  }
};

// Process the response for the 'getCurrentClientServiceRates' API
// and set local variables based on result
function setCurrentClientFuelSurcharge(
  fuelSurcharge: ClientFuelSurchargeRate[] | null,
) {
  isExpiredFuelSurcharge.value = !fuelSurcharge ? true : false;
  activeFuelSurchargeId.value = fuelSurcharge?.[0]?.id ?? null;
  clientRateData.activeFuelSurcharge = true;
  if (!fuelSurcharge) {
    showAppNotification('Client currently has no active Fuel Surcharge.');
  }
}

// Process the response for the 'getAllClientFuelSurcharge' API
// and set local variables based on result
function setAllClientFuelSurchargeRates(
  fuelSurchargeList: ClientFuelSurchargeRate[],
) {
  fuelSurchargeList = fuelSurchargeList.map((f: ClientFuelSurchargeRate) =>
    Object.assign(new ClientFuelSurchargeRate(), f),
  );
  allFuelSurchargeList.value = fuelSurchargeList;
  clientRateData.allFuelSurcharges = true;
}

function closeDocumentUpdatedDialog(): void {
  documentUpdatedDialogIsOpen.value = false;
  if (!clientDetails.value || clientDetails.value.clientId) {
    return;
  }
}

// exit client and push user back to main client page
const exitClient = (): void => {
  router.push({
    name: 'Client',
  });
};

// Handle response to 'getAllClientServiceRates' request, containing a list of
// all ClientServiceRates (short list - without rate table items)
const setClientServiceRateList = (serviceRates: ClientServiceRate[]): void => {
  if (!selectedClientId.value) {
    return;
  }
  if (serviceRates.length === 0) {
    clientRateData.allServiceRates = true;
    allClientServiceRates.value = [];
    return;
  } else if (serviceRates[0].clientId === selectedClientId.value) {
    // we sort the service rates so the latest ones are at the top of the table
    allClientServiceRates.value = serviceRates.reduce(
      (accumulator: ClientServiceRate[], value: ClientServiceRate) => {
        const nextIndex = accumulator.findIndex(
          (i: ClientServiceRate) =>
            value.validToDate &&
            i.validToDate &&
            value.validToDate > i.validToDate,
        );
        const index = nextIndex > -1 ? nextIndex : accumulator.length;
        accumulator.splice(index, 0, value);
        return accumulator;
      },
      [],
    );
  }
  clientRateData.allServiceRates = true;
};

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

// updates the clients local state when there is an update to the clients integration fields
function setClientImportUserUpdate(response: ClientIntegrationUpdate | null) {
  if (
    clientDetails.value === null ||
    response === null ||
    response.clientId !== clientDetails.value.clientId
  ) {
    return;
  }
  clientDetails.value.importTransformationType = response.importUser
    ? response.importUser
    : null;
}

/**
 * Adds a client person mongo id to this clients clientPersonDispatchers array and refreshes the client contact list
 * @param {string} clientPerson_id - The client person _id that will be added to this client.
 * @returns {void}
 */
const addClientPersonDispatcherId = (clientPerson_id: string): void => {
  if (clientDetails.value && clientDetails.value._id) {
    clientDetails.value.clientPersonDispatchers.push(clientPerson_id);
    userManagementStore.getClientPersonsWithAuthDetails(
      clientDetails.value._id,
      clientDetails.value.clientPersonDispatchers,
      true,
    );
  }
};

/**
 * Used as a handler for the savedNewClientPerson event. Updates the clients
 * default dispatcher id if required.
 * @param saveNewClientPersonResponse - The response from the
 * savedNewClientPerson event.
 */
function handleSavedClientPersonResponse(
  saveNewClientPersonResponse: SaveNewClientPersonResponse | null,
) {
  if (
    clientDetails.value &&
    saveNewClientPersonResponse?.clientId === clientDetails.value._id &&
    saveNewClientPersonResponse?.clientPerson?._id
  ) {
    const clientPerson_id = saveNewClientPersonResponse.clientPerson._id;
    const isDefaultDispatcher = saveNewClientPersonResponse.defaultDispatcher;
    if (
      isDefaultDispatcher &&
      clientDetails.value.defaultDispatcherId !==
        saveNewClientPersonResponse.clientPerson._id
    ) {
      clientDetails.value.defaultDispatcherId = clientPerson_id;
    } else if (
      !isDefaultDispatcher &&
      clientDetails.value.defaultDispatcherId ===
        saveNewClientPersonResponse.clientPerson._id
    ) {
      clientDetails.value.defaultDispatcherId = '';
    }
  }
}

/**
 * Handles the response from the updateRoleAccess event. Updates the clients
 * default dispatcher id if required.
 * @param response - The response from the updateRoleAccess event, containing
 * the updated role access details.
 */
function handleRoleAccessResponse(response: UpdateRoleAccessResponse | null) {
  if (
    !clientDetails.value ||
    !response?.clientId ||
    clientDetails.value._id !== response.clientId ||
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList
      ?.client_id !== clientDetails.value._id
  ) {
    // update not for this client.
    return;
  }

  const clientPersonWithAuthDetails: ClientPersonWithAuthDetails | undefined =
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList.clientPersonWithAuthDetailsList.find(
      (x: ClientPersonWithAuthDetails) =>
        x.clientPerson.authRefId === response.authRefId,
    );
  if (!clientPersonWithAuthDetails) {
    return;
  }
  const clientPerson_id: string | undefined =
    clientPersonWithAuthDetails.clientPerson._id;
  if (!clientPerson_id) {
    return;
  }

  const isDefaultDispatcher = response.defaultDispatcher;

  const isClientsDefaultDispatcher: boolean =
    clientDetails.value.defaultDispatcherId === clientPerson_id;

  // Check if the update should remove or update the clients default dispatcher id
  if (isClientsDefaultDispatcher && !isDefaultDispatcher) {
    clientDetails.value.defaultDispatcherId = '';
  } else if (!isClientsDefaultDispatcher && isDefaultDispatcher) {
    clientDetails.value.defaultDispatcherId = clientPerson_id;
  }
}

/**
 * Called by mitt listener when a client save event is received. Used to reset
 * the client details if the client has been updated by another user.
 */
function handleExternalSave(incomingClient: ClientDetails | null): void {
  if (!incomingClient?.clientId) {
    return;
  }
  if (
    incomingClient.clientId === clientDetails.value?.clientId &&
    !awaitingClientDetailsSaveResponse.value
  ) {
    // Show notification if currently in edit mode
    if (isEdited.value) {
      showAppNotification(
        `${clientName.value} has been updated by another user. Your changes have been discarded.`,
        HealthLevel.WARNING,
      );
    }
    isEdited.value = false;
    findAndSetSelectedClient();
  }
}

/**
 * Handles fuel surcharge configuration updates from child component
 */
function handleFuelSurchargeConfigUpdate(value: boolean): void {
  if (clientDetails.value) {
    clientDetails.value.expiredFuelSurchargeDefaultsToDivisionRate = value;
  }
}

/**
 * Handles service rate configuration updates from child component
 */
function handleServiceRateConfigUpdate(value: boolean): void {
  if (clientDetails.value) {
    clientDetails.value.expiredServiceRateDefaultsToDivisionRate = value;
  }
}

/**
 * Handles division rates configuration updates from child component
 */
function handleDivisionRatesConfigUpdate(value: boolean): void {
  if (clientDetails.value) {
    clientDetails.value.usesStandardDivisionRates = value;
  }
}

onBeforeMount(() => {
  if (clientDetailsStore.selectedClientDetailsView) {
    selectedViewType.value = clientDetailsStore.selectedClientDetailsView;
    clientDetailsStore.setSelectedClientDetailsView(null);
  } else {
    selectedViewType.value = 'KEY';
  }
});

onMounted(() => {
  findAndSetSelectedClient();
});

onBeforeUnmount(() => {
  clientDetailsStore.setSelectedClientDetailsView(null);
  clientDetailsStore.setSelectedClientId(null);
});

/**
 * Listens for saved client person responses and role update responses from
 * division users. Sets the clients default dispatcher if required.
 */
useMittListener('savedNewClientPerson', handleSavedClientPersonResponse);
useMittListener(
  'savedNewClientPersonFromClientPortal',
  handleSavedClientPersonResponse,
);
useMittListener('updatedRoleAccessResponse', handleRoleAccessResponse);
useMittListener('clientImportUserUpdate', setClientImportUserUpdate);
useMittListener('savedClientDetails', handleExternalSave);
</script>

<style lang="scss" scoped>
.status-badge {
  border-radius: $border-radius-base;
  font-weight: 600;
  padding: 0px 12px;
  margin-left: auto;
  max-width: fit-content;
}
</style>
