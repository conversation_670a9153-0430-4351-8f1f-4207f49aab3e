<template>
  <div class="zone-location-manager">
    <!-- Existing Locations List -->
    <div v-if="localLocations.length > 0" class="mb-3">
      <v-layout row wrap>
        <v-flex
          v-for="(location, index) in localLocations"
          :key="`location-${index}`"
          md12
          class="mb-2"
        >
          <v-card flat class="location-item">
            <v-layout align-center class="pa-2">
              <v-flex grow>
                <span class="location-text">
                  {{ location.suburb }}, {{ location.postcode }},
                  {{ location.state }}
                </span>
              </v-flex>
              <v-flex shrink>
                <v-btn
                  icon
                  small
                  @click="editLocation(index)"
                  :disabled="disabled"
                >
                  <v-icon small>edit</v-icon>
                </v-btn>
                <v-btn
                  icon
                  small
                  @click="removeLocation(index)"
                  :disabled="disabled"
                >
                  <v-icon small color="error">delete</v-icon>
                </v-btn>
              </v-flex>
            </v-layout>
          </v-card>
        </v-flex>
      </v-layout>
    </div>

    <!-- Add New Location Section -->
    <div class="add-location-section">
      <v-layout v-if="!showAddForm" justify-start>
        <v-btn
          color="blue"
          depressed
          small
          @click="showAddLocationForm"
          :disabled="disabled"
        >
          <v-icon left small>add</v-icon>
          Add Location
        </v-btn>
      </v-layout>

      <!-- Add Location Form -->
      <v-card v-if="showAddForm" flat class="add-location-form pa-3">
        <v-layout row wrap>
          <v-flex md12 class="mb-2">
            <h6>Add New Location</h6>
          </v-flex>
          <v-flex md12 class="mb-3">
            <AddressSuburbSearch
              :address="newLocationAddress"
              :selectedSuburbProp="newLocationSuburb"
              @suburbSelected="onSuburbSelected"
              :enableSuburbSelect="true"
            />
          </v-flex>
          <v-flex md12>
            <v-layout justify-end>
              <v-btn text small @click="cancelAddLocation" :disabled="disabled">
                Cancel
              </v-btn>
              <v-btn
                color="blue"
                small
                depressed
                @click="confirmAddLocation"
                :disabled="disabled || !isNewLocationValid"
              >
                Add
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-card>
    </div>

    <!-- Edit Location Dialog -->
    <v-dialog v-model="showEditDialog" max-width="500px">
      <v-card>
        <v-card-title>Edit Location</v-card-title>
        <v-card-text>
          <AddressSuburbSearch
            :address="editLocationAddress"
            :selectedSuburbProp="editingSuburb"
            @suburbSelected="onEditSuburbSelected"
            :enableSuburbSelect="true"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn text @click="cancelEditLocation">Cancel</v-btn>
          <v-btn
            color="blue"
            depressed
            @click="confirmEditLocation"
            :disabled="!isEditLocationValid"
          >
            Update
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import AddressSuburbSearch from '@/components/common/addressing/address-search-au/address_suburb_search.vue';
import { AddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { computed, ref, watch } from 'vue';

interface Props {
  locations: ZoneLocationSummary[];
  clientId: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const emit = defineEmits<{
  (event: 'locationsChanged', locations: ZoneLocationSummary[]): void;
}>();

// Reactive data
const localLocations = ref<ZoneLocationSummary[]>([]);
const showAddForm = ref(false);
const showEditDialog = ref(false);
const editingIndex = ref(-1);

// New location form
const newLocationSuburb = ref<SuburbAU | null>(null);
const newLocationAddress = ref(new AddressAU());

// Edit location form
const editingSuburb = ref<SuburbAU | null>(null);
const editLocationAddress = ref(new AddressAU());

// Computed properties
const isNewLocationValid = computed(() => {
  return (
    newLocationSuburb.value &&
    newLocationSuburb.value.name &&
    newLocationSuburb.value.postcode &&
    newLocationSuburb.value.state
  );
});

const isEditLocationValid = computed(() => {
  return (
    editingSuburb.value &&
    editingSuburb.value.name &&
    editingSuburb.value.postcode &&
    editingSuburb.value.state
  );
});

// Methods
const showAddLocationForm = () => {
  showAddForm.value = true;
  newLocationSuburb.value = null;
};

const cancelAddLocation = () => {
  showAddForm.value = false;
  newLocationSuburb.value = null;
};

const onSuburbSelected = (suburb: SuburbAU) => {
  newLocationSuburb.value = suburb;
};

const confirmAddLocation = () => {
  if (isNewLocationValid.value && newLocationSuburb.value) {
    const newLocation: ZoneLocationSummary = {
      suburb: newLocationSuburb.value.name,
      postcode: newLocationSuburb.value.postcode,
      state: newLocationSuburb.value.state,
      pudId: '', // Not used for zone management
      zoneName: '', // Will be set by parent
    };

    // Check for duplicates
    const isDuplicate = localLocations.value.some(
      (location) =>
        location.suburb === newLocation.suburb &&
        location.postcode === newLocation.postcode &&
        location.state === newLocation.state,
    );

    if (!isDuplicate) {
      const updatedLocations = [...localLocations.value, newLocation];
      localLocations.value = updatedLocations;
      // Emit the change immediately without triggering watchers
      emit('locationsChanged', updatedLocations);
      cancelAddLocation();
    } else {
      // Could show a notification here about duplicate
      console.warn('Location already exists in this zone');
    }
  }
};

const editLocation = (index: number) => {
  editingIndex.value = index;
  const location = localLocations.value[index];
  editingSuburb.value = {
    name: location.suburb,
    postcode: location.postcode,
    state: location.state,
  } as SuburbAU;
  showEditDialog.value = true;
};

const onEditSuburbSelected = (suburb: SuburbAU) => {
  editingSuburb.value = suburb;
};

const confirmEditLocation = () => {
  if (
    isEditLocationValid.value &&
    editingSuburb.value &&
    editingIndex.value >= 0
  ) {
    const updatedLocation: ZoneLocationSummary = {
      suburb: editingSuburb.value.name,
      postcode: editingSuburb.value.postcode,
      state: editingSuburb.value.state,
      pudId: localLocations.value[editingIndex.value].pudId,
      zoneName: localLocations.value[editingIndex.value].zoneName,
    };

    const updatedLocations = [...localLocations.value];
    updatedLocations[editingIndex.value] = updatedLocation;
    localLocations.value = updatedLocations;
    // Emit the change immediately without triggering watchers
    emit('locationsChanged', updatedLocations);
    cancelEditLocation();
  }
};

const cancelEditLocation = () => {
  showEditDialog.value = false;
  editingIndex.value = -1;
  editingSuburb.value = null;
};

const removeLocation = (index: number) => {
  const updatedLocations = localLocations.value.filter((_, i) => i !== index);
  localLocations.value = updatedLocations;
  // Emit the change immediately without triggering watchers
  emit('locationsChanged', updatedLocations);
};

// Watch for initial props.locations change (only when component mounts or props change externally)
let isInitialized = false;
watch(
  () => props.locations,
  (newLocations) => {
    // Only update if this is the initial load or if the change came from outside
    if (!isInitialized || newLocations.length !== localLocations.value.length) {
      localLocations.value = [...newLocations];
      isInitialized = true;
    }
  },
  { immediate: true, deep: true },
);
</script>

<style lang="scss" scoped>
.zone-location-manager {
  width: 100%;
}

.location-item {
  border: 1px solid #333;
  border-radius: 4px;
  background-color: #2a2a2a;
}

.location-text {
  color: #fff;
  font-size: 14px;
}

.add-location-form {
  border: 1px solid #444;
  border-radius: 4px;
  background-color: #2a2a2a;
  margin-top: 8px;
}

.add-location-section {
  margin-top: 16px;
}
</style>
