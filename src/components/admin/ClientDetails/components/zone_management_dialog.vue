<template>
  <ContentDialog
    :showDialog.sync="dialogIsOpen"
    :title="dialogTitle"
    :width="dialogWidth"
    contentPadding="pa-0"
    @cancel="closeDialog"
    @confirm="saveChanges"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="isSaveDisabled"
    :isLoading="isLoading"
    :confirmBtnText="confirmButtonText"
  >
    <v-flex>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <!-- Create Zone Mode -->
          <div v-if="dialogMode === 'create'">
            <v-tabs
              v-model="createTab"
              dark
              tabs
              centered
              color="#24232a"
              slider-color="yellow"
            >
              <v-tab>New Zone</v-tab>
              <v-tab>Add to Existing Zone</v-tab>
            </v-tabs>

            <v-tabs-items v-model="createTab" class="pa-3">
              <!-- New Zone Tab -->
              <v-tab-item>
                <v-layout row wrap>
                  <v-flex md12 class="mb-3">
                    <v-text-field
                      v-model="formData.zoneName"
                      label="Zone Name"
                      :rules="[rules.required]"
                      solo
                      flat
                      class="v-solo-custom"
                    />
                  </v-flex>

                  <!-- Zone Locations Management -->
                  <v-flex md12>
                    <h6 class="mb-2">Zone Locations</h6>
                    <ZoneLocationManager
                      :locations="formData.locations"
                      :clientId="clientId"
                      @locationsChanged="onLocationsChanged"
                    />
                  </v-flex>
                </v-layout>
              </v-tab-item>

              <!-- Add to Existing Zone Tab -->
              <v-tab-item>
                <v-layout row wrap>
                  <v-flex md12 class="mb-3">
                    <v-select
                      v-model="formData.selectedExistingZone"
                      :items="existingZoneOptions"
                      item-text="label"
                      item-value="value"
                      label="Select Existing Zone"
                      :rules="[rules.required]"
                      solo
                      flat
                      class="v-solo-custom"
                    />
                  </v-flex>

                  <!-- Zone Locations Management -->
                  <v-flex md12>
                    <h6 class="mb-2">Add Locations to Zone</h6>
                    <ZoneLocationManager
                      :locations="formData.locations"
                      :clientId="clientId"
                      @locationsChanged="onLocationsChanged"
                    />
                  </v-flex>
                </v-layout>
              </v-tab-item>
            </v-tabs-items>
          </div>

          <!-- Edit Zone Mode -->
          <div v-else-if="dialogMode === 'edit'" class="pa-3">
            <v-layout row wrap>
              <v-flex md12 class="mb-3">
                <v-text-field
                  v-model="formData.zoneName"
                  label="Zone Name"
                  :rules="[rules.required]"
                  solo
                  flat
                  class="v-solo-custom"
                />
              </v-flex>

              <!-- Zone Locations Management -->
              <v-flex md12>
                <h6 class="mb-2">Zone Locations</h6>
                <ZoneLocationManager
                  :locations="formData.locations"
                  :clientId="clientId"
                  @locationsChanged="onLocationsChanged"
                />
              </v-flex>
            </v-layout>
          </div>

          <!-- Edit Location Mode -->
          <div v-else-if="dialogMode === 'editLocation'" class="pa-3">
            <v-layout row wrap>
              <v-flex md12>
                <h6 class="mb-2">Edit Location</h6>
                <AddressSuburbSearch
                  :address="editLocationAddress"
                  :selectedSuburbProp="selectedSuburb"
                  @suburbSelected="onSuburbSelected"
                  :enableSuburbSelect="true"
                />
              </v-flex>
            </v-layout>
          </div>

          <!-- Bulk Edit Mode -->
          <div v-else-if="dialogMode === 'bulk'" class="pa-3">
            <v-layout row wrap>
              <v-flex md12 class="mb-3">
                <h6>Bulk Edit {{ selectedZones.length }} Zone(s)</h6>
                <v-divider class="my-2" />
              </v-flex>

              <v-flex md12 class="mb-3">
                <v-text-field
                  v-model="formData.bulkZoneName"
                  label="Zone Name (leave empty to keep existing)"
                  solo
                  flat
                  class="v-solo-custom"
                />
              </v-flex>

              <!-- Bulk Location Operations -->
              <v-flex md12>
                <h6 class="mb-2">Location Operations</h6>
                <v-layout row wrap>
                  <v-flex md6>
                    <v-btn
                      color="blue"
                      depressed
                      block
                      @click="showBulkAddLocations = true"
                    >
                      Add Locations to All
                    </v-btn>
                  </v-flex>
                  <v-flex md6>
                    <v-btn
                      color="orange"
                      depressed
                      block
                      @click="showBulkRemoveLocations = true"
                    >
                      Remove Locations from All
                    </v-btn>
                  </v-flex>
                </v-layout>

                <!-- Bulk Add Locations -->
                <div v-if="showBulkAddLocations" class="mt-3">
                  <h6 class="mb-2">Add Locations</h6>
                  <ZoneLocationManager
                    :locations="formData.bulkAddLocations"
                    :clientId="clientId"
                    @locationsChanged="onBulkAddLocationsChanged"
                  />
                </div>

                <!-- Bulk Remove Locations -->
                <div v-if="showBulkRemoveLocations" class="mt-3">
                  <h6 class="mb-2">Remove Locations</h6>
                  <ZoneLocationManager
                    :locations="formData.bulkRemoveLocations"
                    :clientId="clientId"
                    @locationsChanged="onBulkRemoveLocationsChanged"
                  />
                </div>
              </v-flex>
            </v-layout>
          </div>
        </v-flex>
      </v-layout>
    </v-flex>
  </ContentDialog>
</template>

<script setup lang="ts">
import AddressSuburbSearch from '@/components/common/addressing/address-search-au/address_suburb_search.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { AddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { UpdateZoneRequest } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/UpdateZoneRequest';
import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';
import { ZoneSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneSummary';
import { useZoneToZoneStore } from '@/store/modules/ZoneToZoneStore';
import { computed, ref, watch, WritableComputedRef } from 'vue';
import ZoneLocationManager from './zone_location_manager.vue';

interface Props {
  showDialog: boolean;
  dialogMode: 'create' | 'edit' | 'editLocation' | 'bulk';
  zoneData?: ZoneSummary | null;
  locationData?: ZoneLocationSummary | null;
  selectedZones?: string[];
  clientId: string;
  existingZones?: ZoneSummary[];
}

const props = withDefaults(defineProps<Props>(), {
  zoneData: null,
  locationData: null,
  selectedZones: () => [],
  existingZones: () => [],
});

const emit = defineEmits<{
  (event: 'close'): void;
  (event: 'save'): void;
}>();

const zoneToZoneStore = useZoneToZoneStore();

// Reactive data
const isLoading = ref(false);
const createTab = ref(0);
const showBulkAddLocations = ref(false);
const showBulkRemoveLocations = ref(false);

// Form data
const formData = ref({
  zoneName: '',
  locations: [] as ZoneLocationSummary[],
  selectedExistingZone: null as number | null,
  bulkZoneName: '',
  bulkAddLocations: [] as ZoneLocationSummary[],
  bulkRemoveLocations: [] as ZoneLocationSummary[],
});

// Location editing
const selectedSuburb = ref<SuburbAU | null>(null);
const editLocationAddress = ref(new AddressAU());

// Validation rules
const rules = {
  required: (value: any) => !!value || 'This field is required',
};

// Computed properties
const dialogIsOpen: WritableComputedRef<boolean> = computed({
  get: () => props.showDialog,
  set: (value: boolean) => {
    if (!value) {
      emit('close');
    }
  },
});

const dialogTitle = computed(() => {
  switch (props.dialogMode) {
    case 'create':
      return 'Create Zone';
    case 'edit':
      return 'Edit Zone';
    case 'editLocation':
      return 'Edit Location';
    case 'bulk':
      return `Bulk Edit ${props.selectedZones.length} Zone(s)`;
    default:
      return 'Zone Management';
  }
});

const dialogWidth = computed(() => {
  return props.dialogMode === 'bulk' ? '900px' : '700px';
});

const confirmButtonText = computed(() => {
  switch (props.dialogMode) {
    case 'create':
      return 'Create Zone';
    case 'edit':
      return 'Save Changes';
    case 'editLocation':
      return 'Update Location';
    case 'bulk':
      return 'Apply Changes';
    default:
      return 'Save';
  }
});

const existingZoneOptions = computed(() => {
  return props.existingZones.map((zone) => ({
    label: `Zone ${zone.zoneId}: ${zone.zoneName}`,
    value: zone.zoneId,
  }));
});

const isSaveDisabled = computed(() => {
  if (props.dialogMode === 'create') {
    if (createTab.value === 0) {
      return !formData.value.zoneName || formData.value.locations.length === 0;
    } else {
      return (
        !formData.value.selectedExistingZone ||
        formData.value.locations.length === 0
      );
    }
  } else if (props.dialogMode === 'edit') {
    return !formData.value.zoneName;
  } else if (props.dialogMode === 'editLocation') {
    return !selectedSuburb.value;
  }
  return false;
});

// Methods
const closeDialog = () => {
  emit('close');
};

const onLocationsChanged = (locations: ZoneLocationSummary[]) => {
  formData.value.locations = [...locations];
};

const onBulkAddLocationsChanged = (locations: ZoneLocationSummary[]) => {
  formData.value.bulkAddLocations = [...locations];
};

const onBulkRemoveLocationsChanged = (locations: ZoneLocationSummary[]) => {
  formData.value.bulkRemoveLocations = [...locations];
};

const onSuburbSelected = (suburb: SuburbAU) => {
  selectedSuburb.value = suburb;
};

const saveChanges = async () => {
  isLoading.value = true;
  try {
    await handleSave();
    emit('save');
  } catch (error) {
    console.error('Failed to save changes:', error);
  } finally {
    isLoading.value = false;
  }
};

const handleSave = async () => {
  switch (props.dialogMode) {
    case 'create':
      await handleCreateZone();
      break;
    case 'edit':
      await handleEditZone();
      break;
    case 'editLocation':
      await handleEditLocation();
      break;
    case 'bulk':
      await handleBulkEdit();
      break;
  }
};

const handleCreateZone = async () => {
  if (createTab.value === 0) {
    // Create new zone
    const request: UpdateZoneRequest = {
      clientId: props.clientId,
      zoneId: null, // Backend will assign
      zoneName: formData.value.zoneName,
      addedZoneLocations: formData.value.locations,
      deletedZoneLocations: [],
    };
    await zoneToZoneStore.updateZone(request);
  } else {
    // Add to existing zone
    if (formData.value.selectedExistingZone) {
      const request: UpdateZoneRequest = {
        clientId: props.clientId,
        zoneId: formData.value.selectedExistingZone,
        zoneName: '', // Keep existing name
        addedZoneLocations: formData.value.locations,
        deletedZoneLocations: [],
      };
      await zoneToZoneStore.updateZone(request);
    }
  }
};

const handleEditZone = async () => {
  if (props.zoneData) {
    const originalLocations = props.zoneData.zoneLocationsList;
    const newLocations = formData.value.locations;

    // Calculate added and deleted locations
    const addedLocations = newLocations.filter(
      (newLoc) =>
        !originalLocations.some(
          (origLoc) =>
            origLoc.suburb === newLoc.suburb &&
            origLoc.postcode === newLoc.postcode &&
            origLoc.state === newLoc.state,
        ),
    );

    const deletedLocations = originalLocations.filter(
      (origLoc) =>
        !newLocations.some(
          (newLoc) =>
            newLoc.suburb === origLoc.suburb &&
            newLoc.postcode === origLoc.postcode &&
            newLoc.state === origLoc.state,
        ),
    );

    const request: UpdateZoneRequest = {
      clientId: props.clientId,
      zoneId: props.zoneData.zoneId,
      zoneName: formData.value.zoneName,
      addedZoneLocations: addedLocations,
      deletedZoneLocations: deletedLocations,
    };
    await zoneToZoneStore.updateZone(request);
  }
};

const handleEditLocation = async () => {
  if (props.locationData && selectedSuburb.value) {
    // Find the zone that contains this location
    const zone = props.existingZones.find((z) =>
      z.zoneLocationsList.some(
        (loc) =>
          loc.suburb === props.locationData!.suburb &&
          loc.postcode === props.locationData!.postcode &&
          loc.state === props.locationData!.state,
      ),
    );

    if (zone) {
      const newLocation: ZoneLocationSummary = {
        suburb: selectedSuburb.value.name,
        postcode: selectedSuburb.value.postcode,
        state: selectedSuburb.value.state,
        pudId: props.locationData.pudId,
        zoneName: zone.zoneName,
      };

      const request: UpdateZoneRequest = {
        clientId: props.clientId,
        zoneId: zone.zoneId,
        zoneName: zone.zoneName,
        addedZoneLocations: [newLocation],
        deletedZoneLocations: [props.locationData],
      };
      await zoneToZoneStore.updateZone(request);
    }
  }
};

const handleBulkEdit = async () => {
  // Process each selected zone
  for (const zoneId of props.selectedZones) {
    const zone = props.existingZones.find(
      (z) => z.zoneId.toString() === zoneId,
    );
    if (zone) {
      const request: UpdateZoneRequest = {
        clientId: props.clientId,
        zoneId: zone.zoneId,
        zoneName: formData.value.bulkZoneName || zone.zoneName,
        addedZoneLocations: formData.value.bulkAddLocations,
        deletedZoneLocations: formData.value.bulkRemoveLocations,
      };
      await zoneToZoneStore.updateZone(request);
    }
  }
};

// Watch for dialog mode changes to reset form
watch(
  () => props.dialogMode,
  () => {
    resetForm();
  },
);

// Watch for zone data changes
watch(
  () => props.zoneData,
  (newZone) => {
    if (newZone && props.dialogMode === 'edit') {
      formData.value.zoneName = newZone.zoneName;
      formData.value.locations = [...newZone.zoneLocationsList];
    }
  },
);

// Watch for location data changes
watch(
  () => props.locationData,
  (newLocation) => {
    if (newLocation && props.dialogMode === 'editLocation') {
      selectedSuburb.value = {
        name: newLocation.suburb,
        postcode: newLocation.postcode,
        state: newLocation.state,
      } as SuburbAU;
    }
  },
);

const resetForm = () => {
  formData.value = {
    zoneName: '',
    locations: [],
    selectedExistingZone: null,
    bulkZoneName: '',
    bulkAddLocations: [],
    bulkRemoveLocations: [],
  };
  selectedSuburb.value = null;
  createTab.value = 0;
  showBulkAddLocations.value = false;
  showBulkRemoveLocations.value = false;
};
</script>

<style lang="scss" scoped>
.dialog-content {
  min-height: 400px;
}

.v-tabs {
  background-color: transparent;
}

.v-tab {
  text-transform: none;
}
</style>
