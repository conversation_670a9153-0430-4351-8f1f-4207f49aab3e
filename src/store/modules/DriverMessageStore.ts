import { handleChatMessage } from '@/helpers/ChatMessageHelpers/ChatMessageHelpers';
import { VacantTruckGroupType } from '@/helpers/JobProgressAlertHelpers/VacantTruckHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import ChatHistoryRequest from '@/interface-models/Generic/ChatConversation/RequestChatHistory';
import SendBulkChatMessageRequest, {
  BulkChatMessageResponse,
} from '@/interface-models/Generic/ChatConversation/SendBulkChatMessageRequest';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import JobProgressAlertHistoryRequest from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertHistoryRequest';
import {
  JobProgressAlertType,
  returnProgressAlertTypeMessage,
} from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertType';
import SaveJobProgressAlertListRequest from '@/interface-models/Jobs/JobProgressAlert/SaveJobProgressAlertListRequest';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import moment from 'moment-timezone';
import { defineStore } from 'pinia';
import { v4 as uuidv4 } from 'uuid';
import { Ref, ref } from 'vue';

export const useDriverMessageStore = defineStore('driverMessageStore', () => {
  const chatMessageList: Ref<ChatMessage[]> = ref([]);
  const pickupProgressAlerts: Ref<Map<number, JobProgressAlert> | null> =
    ref(null);
  const acceptProgressAlerts: Ref<Map<number, JobProgressAlert> | null> =
    ref(null);
  const loadTimeProgressAlerts: Ref<Map<number, JobProgressAlert> | null> =
    ref(null);

  const expandedVacantTruckGroups: Ref<VacantTruckGroupType[]> = ref([
    VacantTruckGroupType.ACTIVE,
    VacantTruckGroupType.INACTIVE,
  ]);

  /**
   * Resets all state variables back to their default values. Called when
   * logging out or changing divisions
   */
  function resetState() {
    chatMessageList.value = [];
    pickupProgressAlerts.value = null;
    acceptProgressAlerts.value = null;
    loadTimeProgressAlerts.value = null;
  }

  /**
   * Add the provided chat message to the list of chat messages. This is used
   * when the frontend adds, such as notifications for job progress alerts or
   * app notifications
   * @param message - the message to add
   */
  function addChatMessageToList(message: ChatMessage) {
    chatMessageList.value.push(message);
  }

  /**
   * Sends request over websocket to fetch a list of JobProgressAlert documents
   * using a set of parameters contained within the request.
   * @param request contains various parameters to filter the results
   * @returns list of JobProgressAlert documents or null if an error occurred
   */
  async function searchJobProgressAlertHistory(
    request: JobProgressAlertHistoryRequest,
  ): Promise<JobProgressAlert[] | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/job/getAlertHistory', request, true),
        'getJobProgressAlertHistoryResponse',
      );
      return result;
    } catch (error) {
      console.error('Error searching job progress alert history: ', error);
      return null;
    }
  }

  /**
   * Handler for division-level message pushed from backend, containing the
   * latest batch of job progress alerts, generated by the scheduler. We will
   * store these in the store such that we can display them in the UI.
   * @param response The response containing the list of JobProgressAlerts
   */
  function savedJobProgressAlertHistoryResponse(
    response: SaveJobProgressAlertListRequest | null,
  ) {
    // Return if the payload is null or the list is empty
    if (!response?.jobProgressAlertList?.length) {
      return;
    }

    const operationsStore = useOperationsStore();

    const alertList: JobProgressAlert[] = response.jobProgressAlertList;
    // Get the users settings for what we display in the chat section. If the
    // setting is disabled for the given type then we will not create a chat
    // message for it
    const showPickupMessage: boolean =
      operationsStore.isMessageListSettingActive(
        'showApproachingPickupTimeAlerts',
      );
    const showAcceptMessage: boolean =
      operationsStore.isMessageListSettingActive('showLateAcceptAlerts');
    const showLoadTimeMessage: boolean =
      operationsStore.isMessageListSettingActive('showLoadTimeAlerts');

    const chatMessageFromAlertList = (
      alerts: JobProgressAlert[],
    ): ChatMessage => {
      const systemMessage = new ChatMessage();
      systemMessage._id = uuidv4();
      systemMessage.timestamp = alerts[0].epochTime;
      systemMessage.senderName = 'SYS';
      systemMessage.content = `${alerts.length} new job${
        alerts.length === 1 ? '' : 's'
      } ${returnProgressAlertTypeMessage(alerts[0].alertType)}`;
      systemMessage.roleId = -1;
      systemMessage.jobId = null;
      systemMessage.receiverId = alerts[0].alertType;
      systemMessage.associatedIds = alerts.map((a) => a._id);

      return systemMessage;
    };
    // Initialise if the maps are null. Their default values are null, and
    // then we set them on the first instance of alerts coming in
    if (pickupProgressAlerts.value === null) {
      pickupProgressAlerts.value = new Map();
    }
    if (loadTimeProgressAlerts.value === null) {
      loadTimeProgressAlerts.value = new Map();
    }
    if (acceptProgressAlerts.value === null) {
      acceptProgressAlerts.value = new Map();
    }

    const pickup: JobProgressAlert[] = [];
    const accept: JobProgressAlert[] = [];
    const load: JobProgressAlert[] = [];
    alertList.forEach((alert: JobProgressAlert) => {
      switch (alert.alertType) {
        case JobProgressAlertType.APPROACHING_PICKUP_TIME:
          // If there's not already an entry in the map then it means it also
          // hasn't appeared as a chat message. We should include it when we
          // display a chat message
          if (!pickupProgressAlerts.value!.has(alert.jobId)) {
            pickup.push(alert);
            pickupProgressAlerts.value!.set(alert.jobId, alert);
          }
          break;
        case JobProgressAlertType.AWAITING_ACCEPT:
          // If there's not already an entry in the map then it means it also
          // hasn't appeared as a chat message. We should include it when we
          // display a chat message
          if (!acceptProgressAlerts.value!.has(alert.jobId)) {
            accept.push(alert);
            acceptProgressAlerts.value!.set(alert.jobId, alert);
          }
          break;
        case JobProgressAlertType.LOAD_TIME:
          // If there's not already an entry in the map then it means it also
          // hasn't appeared as a chat message. We should include it when we
          // display a chat message
          if (!loadTimeProgressAlerts.value!.has(alert.jobId)) {
            load.push(alert);
            loadTimeProgressAlerts.value!.set(alert.jobId, alert);
          }
          break;
      }
    });
    // If there are any new in the arrays, then it means there were new values
    // for that type of alert. In that case we should re-init the map to
    // maintain reactivity
    if (pickup.length) {
      pickupProgressAlerts.value = new Map(pickupProgressAlerts.value);
    }
    if (accept.length) {
      acceptProgressAlerts.value = new Map(acceptProgressAlerts.value);
    }
    if (load.length) {
      loadTimeProgressAlerts.value = new Map(loadTimeProgressAlerts.value);
    }
    // Generate a chat message for each list of progress alerts, which will be
    // displayed in the chat message section. If the setting is not enabled
    // for a particular type, then we will not show the message
    [
      showPickupMessage ? pickup : [],
      showAcceptMessage ? accept : [],
      showLoadTimeMessage ? load : [],
    ].forEach((al: JobProgressAlert[]) => {
      if (al.length > 0) {
        const msg = chatMessageFromAlertList(al);
        chatMessageList.value.push(msg);
      }
    });
  }

  /**
   * Request and response for fetching a list of ChatMessage objects for a
   * driver. Called from driver chat components (history and conversation)
   * @param request Includes parameters for start epoch and end epoch to filter
   * results.
   * @returns list of ChatMessages
   */
  async function requestChatMessagesForDriverId(request: ChatHistoryRequest) {
    try {
      if (
        !request.startEpoch ||
        !request.endEpoch ||
        request.startEpoch > request.endEpoch
      ) {
        throw new Error('Invalid date parameters for chat messages search');
      }
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/chat/listChatMessagesForDriverId',
          request,
          true,
        ),
        'chatHistoryForDriver',
        {
          mapResponse: (payload) =>
            !request.driverId ||
            !payload?.length ||
            (!!request.driverId &&
              (payload[0].senderId === request.driverId ||
                payload[0].receiverId === request.driverId)),
        },
      );
      return result;
    } catch (error) {
      console.error('Error requesting chat messages for driver: ', error);
      return null;
    }
  }

  /**
   * Request and response for updating a ChatMessage from the Operations
   * Dashboard. The only values that are allowed to be updated are
   * * isNewChat
   * * isActioned
   * * actionRequired
   * @param payload The chat message to update
   * @returns The chat message that was updated
   */
  async function updateChatMessage(
    payload: ChatMessage,
  ): Promise<ChatMessage | null> {
    try {
      // Send request over websocket
      return sendRequestAndListenForResponse(
        new WebSocketRequest('/chat/updateChatMessage', payload, true),
        'updatedMessage',
      );
    } catch (error) {
      console.error('Error updating chat message: ', error);
      return null;
    }
  }

  /**
   * Request and response for sending a chat message to a driver from the
   * Operations portal. The message will be sent to the driver's mobile app, and
   * also published to all other frontend users.
   * @param payload The chat message to send
   * @returns The chat message that was sent
   */
  async function sendChatMessage(
    payload: ChatMessage,
  ): Promise<ChatMessage | null> {
    try {
      if (payload.jobId === 0) {
        payload.jobId = null;
      }
      // Send request over websocket
      return sendRequestAndListenForResponse(
        new WebSocketRequest('/chat/sendMessage', payload, true),
        'publishedMessage',
      );
    } catch (error) {
      console.error('Error sending chat message: ', error);
      return null;
    }
  }
  /**
   * Request and response for the list of recent chat messages. Called on app
   * mount. The response is stored in the store for use in the Driver Chat
   * components on the operations dashboard.
   */
  async function requestChatMessageList() {
    try {
      const userLocale = useCompanyDetailsStore().userLocale;
      const dayOfWeek = moment.tz(userLocale).day();
      // If day of week is 1 (MONDAY) then subtract 3 days to get messages from Friday. Otherwise subtract 1 day
      const backdateCount = dayOfWeek === 1 ? 3 : 1;
      // get chat messages
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/chat/retrieveHistory',
          moment
            .tz(userLocale)
            .subtract(backdateCount, 'days')
            .startOf('day')
            .valueOf(),
          true,
        ),
        'chatHistory',
      );
      if (result) {
        updateChatMessageState(result);
      }
      return result;
    } catch (error) {
      console.error('Error requesting chat message list: ', error);
      return null;
    }
  }

  /**
   * Updates the chat message state with the provided payload. It filters the
   * payload to only include messages that are not actioned or require action.
   * The resulting chat history is sorted in ascending order based on the
   * timestamp.
   *
   * @param {ChatMessage[]} payload - An array of chat messages to update the
   * state with.
   */
  function updateChatMessageState(payload: ChatMessage[]) {
    const chatHistory: ChatMessage[] = [];
    for (const chat of payload) {
      if (!chat.isActioned || chat.actionRequired) {
        chatHistory.push(Object.assign(new ChatMessage(), chat));
      }
    }
    // Sort in ascending order based on timestamp
    chatHistory.sort(
      (a: ChatMessage, b: ChatMessage) =>
        (a.timestamp ? a.timestamp : 0) - (b.timestamp ? b.timestamp : 0),
    );
    chatMessageList.value = chatHistory;
    useRootStore().operationsPortalLoadedData.CHAT_HISTORY = true;
  }

  /**
   * Request and response for removing a ChatMessage from a job. Called from the
   * Operations driver chat components. Backend will remove the ChatMessage from
   * the job's notes.
   * @param payload The chat message to remove
   * @returns The chat message that was removed
   */
  async function removeChatMessageFromJob(
    payload: ChatMessage,
  ): Promise<ChatMessage | null> {
    try {
      // Send request over websocket
      return sendRequestAndListenForResponse(
        new WebSocketRequest('/chat/removeChatMessageFromJob', payload, true),
        'removedChatMessage',
      );
    } catch (error) {
      console.error('Error removing chat message from job: ', error);
      return null;
    }
  }

  /**
   * Request and response for associating a ChatMessage with a job. Called from
   * the Operations driver chat components. Backend will add the ChatMessage to
   * the job's notes.
   * @param chatMessage The chat message to associate with a job
   * @param jobId The job ID to associate the chat message with
   * @returns The chat message that was added
   */
  async function addChatMessageToJob(
    chatMessage: ChatMessage,
    jobId: number,
  ): Promise<ChatMessage | null> {
    try {
      const request: ChatMessage = JSON.parse(JSON.stringify(chatMessage));
      request.jobId = jobId;
      // Send request over websocket
      return sendRequestAndListenForResponse(
        new WebSocketRequest('/chat/addChatMessageToJob', request, true),
        'addedChatMessage',
      );
    } catch (error) {
      console.error('Error adding chat message to job: ', error);
      return null;
    }
  }

  /**
   * Handles the receipt of a chat message from the backend. Called from
   * GlobalEventListeners chat message received, after a variety of operations.
   * These include sending a chat message, updating a chat message, removing a
   * chat message from a job, and adding a chat message to a job.
   * @param payload The chat message to handle
   */
  function receivedChatMessage(payload: ChatMessage | null) {
    if (payload !== null) {
      const message = handleChatMessage(payload, true);
      // If the message is not actioned, then add it to the list of messages if
      // it doesn't already exist, or replace it if it is already in the list
      const foundIndex = chatMessageList.value.findIndex(
        (cm) => cm._id === message._id,
      );
      if (!message.isActioned || message.actionRequired) {
        if (foundIndex !== -1) {
          chatMessageList.value.splice(foundIndex, 1, message);
        } else {
          chatMessageList.value.push(message);
        }
      } else {
        // If the message is actioned, then remove it from the list if it
        // exists
        if (foundIndex !== -1) {
          chatMessageList.value.splice(foundIndex, 1);
        }
      }
    }
  }

  /**
   * Updates the system chat message in the chat message list.
   * @param message The chat message to update.
   */
  function updateSystemChatMessage(message: ChatMessage) {
    const foundIndex = chatMessageList.value.findIndex(
      (cm) => cm._id === message._id,
    );
    if (foundIndex === -1) {
      return;
    }
    chatMessageList.value.splice(foundIndex, 1);
  }

  /**
   * Request and response for sending a bulk chat message to a list of
   * driverIds. Called from the DriverBulkMessage component.
   * @param request The request containing the driverIds and message content
   */
  async function dispatchBulkChatMessageRequest(
    request: SendBulkChatMessageRequest,
  ): Promise<BulkChatMessageResponse | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/chat/sendBulkChatMessage', request, true),
        'receivedBulkChatMessageResponse',
      );
      return result;
    } catch (error) {
      console.error('Error dispatching bulk chat message request: ', error);
      return null;
    }
  }

  /**
   * Sets the list of expanded vacant truck groups, for persistence of expansion
   * selections in VacantTrucksTable component.
   *
   * @param groups - An array of `VacantTruckGroupType` representing the groups
   * to be expanded.
   */
  function setExpandedVacantTruckGroups(groups: VacantTruckGroupType[]) {
    expandedVacantTruckGroups.value = groups;
  }

  return {
    chatMessageList,
    pickupProgressAlerts,
    acceptProgressAlerts,
    loadTimeProgressAlerts,
    expandedVacantTruckGroups,
    resetState,
    savedJobProgressAlertHistoryResponse,
    requestChatMessagesForDriverId,
    dispatchBulkChatMessageRequest,
    searchJobProgressAlertHistory,
    updateChatMessage,
    sendChatMessage,
    requestChatMessageList,
    updateChatMessageState,
    removeChatMessageFromJob,
    addChatMessageToJob,
    receivedChatMessage,
    updateSystemChatMessage,
    addChatMessageToList,
    setExpandedVacantTruckGroups,
  };
});
