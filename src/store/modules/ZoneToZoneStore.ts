import {
  initialiseClientServiceRate,
  initialiseFleetAssetServiceRate,
} from '@/helpers/classInitialisers/InitialiseServiceRate';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { showNotificationList } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { FleetAssetServiceRate } from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { FleetAssetServiceRateResponse } from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRateResponse';
import { UpdateZoneRequest } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/UpdateZoneRequest';
import { ZoneRateAvailableResponse } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneRateAvailableResponse';
import { ZoneSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneSummary';
import { ZoneToZoneServiceRateRequest } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneServiceRateRequest';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import { defineStore } from 'pinia';

export const useZoneToZoneStore = defineStore('zoneToZoneStore', () => {
  /**
   * Request and response for adding or updating any zone information including
   * zone name and zone locations for the requested client.
   * @param request - UpdateZoneRequest object containing the clientId, zoneId,
   * zoneName, addedZoneLocations, and deletedZoneLocations.
   * @returns a ZoneSummary object containing details of the updated Zone, or
   * null if the request fails.
   */
  async function updateZone(
    request: UpdateZoneRequest,
  ): Promise<ZoneSummary | null> {
    try {
      if (!request.clientId) {
        logConsoleError(
          'Missing required parameters when updating ZoneToZone zone',
          request,
        );
        return null;
      }
      // Send request over websocket
      const result: ZoneSummary | null = await sendRequestAndListenForResponse(
        new WebSocketRequest('/zoneToZone/updateZoneForClient', request, true),
        'updatedZoneResponse',
        {
          mapResponse: (response) =>
            request.clientId === response?.clientId &&
            (!request.zoneId || request.zoneId === response?.zoneId),
        },
      );
      return result;
    } catch (error) {
      logConsoleError(
        'Something went wrong when updating ZoneToZone zone',
        request,
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for listing all the zones including zone locations for
   * the requested client.
   * @param clientId - The clientId for which to request the zone list.
   * @returns - An array of ZoneSummary objects containing details of the zones
   */
  async function requestZoneListForClient(
    clientId: string,
  ): Promise<ZoneSummary[] | null> {
    try {
      if (!clientId) {
        throw new Error(
          'Missing required parameters when requesting ZoneToZone zone list',
        );
      }
      // Send request over websocket
      const result: ZoneSummary[] | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest(
            '/zoneToZone/listAllZonesForClient',
            clientId,
            false,
          ),
          'selectedClientZoneList',
          {
            mapResponse: (response) =>
              !response?.length || response[0].clientId === clientId,
          },
        );
      return result;
    } catch (error) {
      logConsoleError(
        'Something went wrong when requesting ZoneToZone zone list for clientId:',
        clientId,
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for fetching zone to zone rates for the requested
   * fleet asset on the requested search date for the requested start suburb to
   * the requested end suburb.
   * @param request - ZoneToZoneServiceRateRequest object containing the
   * fleetAssetId (clientOrFleetAssetId), start and end suburbs, and the date to
   * look query for
   * @returns - A FleetAssetServiceRate object containing the zone to zone rate details, or
   * null if the request fails.
   */
  async function requestZoneToZoneFleetAssetRates(
    request: ZoneToZoneServiceRateRequest,
  ): Promise<FleetAssetServiceRate | null> {
    try {
      if (!request.clientOrFleetAssetId || !request.searchDate) {
        throw new Error(
          'Missing required parameters when requesting ZoneToZone fleet asset rates',
        );
      }
      // Send request over websocket
      const result: FleetAssetServiceRateResponse | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest(
            '/fleetAssetServiceRate/getZoneToZoneFleetAssetServiceRates',
            request,
            true,
          ),
          'selectedZoneToZoneFleetAssetServiceRateResponse',
          {
            mapResponse: (response) => {
              // In case of null response, we can't tell if the request is a
              // match via id, so return true
              if (!response) {
                return true;
              }

              // Validate that the response has the same id as the request
              if (
                request.clientOrFleetAssetId !== response.fleetAssetId &&
                response.fleetAssetId !== '0'
              ) {
                return false;
              }

              // If serviceTypeIds was provided and the response has a rateTableItem in it,
              // check if the serviceTypeId matches. Otherwise, if the response was empty
              // or serviceTypeIds was not provided, return the response.
              if (
                request.serviceTypeIds?.length &&
                response?.fleetAssetServiceRate?.rateTableItems[0]
              ) {
                const serviceTypeIdMatches =
                  response.fleetAssetServiceRate.rateTableItems[0]
                    .serviceTypeId === request.serviceTypeIds[0];
                return serviceTypeIdMatches;
              }
              return true;
            },
          },
        );
      return result
        ? initialiseFleetAssetServiceRate(result.fleetAssetServiceRate)
        : null;
    } catch (error) {
      logConsoleError(
        'Something went wrong when requesting ZoneToZone fleet asset rates',
        request,
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for checking whether the client has zone to zone rates
   * (rateTypeId: 7) available for the provided search date.
   * @param request - ServiceRateTableRequest object containing the clientId and
   * searchDate
   * @returns - A ZoneRateAvailableResponse object containing the availability of
   * the zone to zone rates, or null if the request fails.
   */
  async function requestZoneToZoneClientRates(
    request: ZoneToZoneServiceRateRequest,
  ): Promise<CurrentClientServiceRateResponse | null> {
    try {
      if (!request.clientOrFleetAssetId || !request.searchDate) {
        throw new Error(
          'Missing required parameters when requesting ZoneToZone client rates',
        );
      }
      // Set endpoint based on portal type
      const endpoint = sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/clientServiceRate/getZoneToZoneClientServiceRates`
        : '/clientServiceRate/getZoneToZoneClientServiceRates';

      // Send request over websocket
      const result: CurrentClientServiceRateResponse | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest(endpoint, request, true),
          'selectedZoneToZoneClientServiceRateResponse',
          {
            mapResponse: (response) => {
              // console.log('mapResponse called with response:', response);

              // Validate that the response has the same id as the request
              if (request.clientOrFleetAssetId !== response?.clientId) {
                // console.log(
                //   'Client or Fleet Asset ID does not match:',
                //   request.clientOrFleetAssetId,
                //   response?.clientId,
                // );
                return false;
              }

              // If serviceTypeIds was provided and the response has a rateTableItem in it,
              // check if the serviceTypeId matches. Otherwise, if the response was empty
              // or serviceTypeIds was not provided, return the response.
              if (
                request.serviceTypeIds?.length &&
                response.clientServiceRate?.rateTableItems[0]
              ) {
                const serviceTypeIdMatches =
                  response.clientServiceRate.rateTableItems[0].serviceTypeId ===
                  request.serviceTypeIds[0];
                // console.log(
                //   `Service Type ID check for ${request.serviceTypeIds[0]}`,
                //   serviceTypeIdMatches,
                // );
                return serviceTypeIdMatches;
              }

              // console.log('Returning true by default');
              return true;
            },
          },
        );
      // Display error messages if any are present in the response
      if (result?.recordedErrorMessages?.length) {
        showNotificationList(result.recordedErrorMessages, {
          title: 'Client Zone to Zone Rates',
          type: HealthLevel.WARNING,
          duration: 2500 * result.recordedErrorMessages.length,
        });
      }
      if (result?.clientServiceRate) {
        // Init ClientServiceRate if result is not null
        result.clientServiceRate = initialiseClientServiceRate(
          result.clientServiceRate,
        );
      }
      return result;
    } catch (error) {
      logConsoleError(
        'Something went wrong when requesting ZoneToZone client rates',
        request,
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for checking whether the client has zone to zone rates
   * (rateTypeId: 7) available for the provided search date.
   * @param request - ServiceRateTableRequest object containing the clientId and
   * searchDate
   * @returns - A ZoneRateAvailableResponse object containing the availability of
   * the zone to zone rates, or null if the request fails.
   */
  async function checkZoneToZoneRatesAvailable(
    request: ZoneToZoneServiceRateRequest,
  ): Promise<ZoneRateAvailableResponse | null> {
    try {
      if (!request.clientOrFleetAssetId || !request.searchDate) {
        throw new Error(
          'Missing required parameters when checking ZoneToZone rates availability',
        );
      }
      // Send request over websocket
      const result: ZoneRateAvailableResponse | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest(
            '/clientServiceRate/checkZonedRatesAreAvailable',
            request,
            true,
          ),
          'zoneRateAvailableResponse',
          {
            mapResponse: (response) =>
              request.clientOrFleetAssetId === response?.clientId,
          },
        );
      return result;
    } catch (error) {
      logConsoleError(
        'Something went wrong when checking ZoneToZone rates availability',
        request,
        error,
      );
      return null;
    }
  }

  // Export the store to use it in the app
  return {
    updateZone,
    requestZoneListForClient,
    requestZoneToZoneFleetAssetRates,
    requestZoneToZoneClientRates,
    checkZoneToZoneRatesAvailable,
  };
});
