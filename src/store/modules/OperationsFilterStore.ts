import { LOCAL_STORAGE_SELECTED_OPERATIONS_CHANNELS } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import {
  combineOperationsChannels,
  returnCleanDefaultChannel,
} from '@/helpers/AppLayoutHelpers/OperationsChannelHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  BroadcastChannelType,
  BroadcastIds,
} from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import { defineStore } from 'pinia';
import { computed, ComputedRef, Ref, ref } from 'vue';

export const useFilterStore = defineStore('filterStore', () => {
  const operationsChannels: Ref<OperationsChannel[]> = ref([]);
  const selectedOperationsChannelIds: Ref<string[] | null> = ref(null);
  const selectedOperationsChannel: Ref<OperationsChannel | null> = ref(null);

  /**
   * Loads initial values for selected service type and vehicle class from local storage.
   * Updates the corresponding reactive properties if values are found in local storage.
   */
  const loadInitialValues = () => {
    const selectedChannelId = localStorage.getItem(
      LOCAL_STORAGE_SELECTED_OPERATIONS_CHANNELS,
    );
    if (selectedChannelId) {
      selectedOperationsChannelIds.value = JSON.parse(selectedChannelId);
    }
  };
  // Load the initial values upon execution
  loadInitialValues();

  /**
   * Clears the selected vehicle class filters for the map an list.
   */
  function clearDriverFilters() {
    setSelectedOperationsChannelIds(null);
  }

  /**
   * Clears the selected service type filters
   */
  function clearJobFilters() {
    setSelectedOperationsChannelIds(null);
  }

  const isOperationsChannelFilterApplied: ComputedRef<boolean> = computed(
    () => {
      if (!selectedOperationsChannel.value) {
        return true;
      } else {
        // Return true if the selected channel is not the default channel
        return !selectedOperationsChannel.value.isDefaultChannel;
      }
    },
  );

  /**
   * Updates selected channel IDs, persists them, refreshes selection, and
   * broadcasts changes if needed.
   *
   * @param channelIds - Selected channel IDs or `null` to clear.
   */
  function setSelectedOperationsChannelIds(channelIds: string[] | null) {
    selectedOperationsChannelIds.value = channelIds;
    if (channelIds) {
      localStorage.setItem(
        LOCAL_STORAGE_SELECTED_OPERATIONS_CHANNELS,
        JSON.stringify(channelIds),
      );
    } else {
      localStorage.removeItem(LOCAL_STORAGE_SELECTED_OPERATIONS_CHANNELS);
    }

    refreshSelectedOperationsChannel();

    // Only send message if isFleetTrackingWindowOpen. Note: this will always
    // be false when we're in the FleetTracking window
    if (useFleetMapStore().isFleetTrackingWindowOpen) {
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.FLEET_TRACKING,
        new BroadcastMessage(
          BroadcastIds.FLEET_TRACKING.TO_EXTERNAL.OPERATIONS_CHANNEL_UPDATED,
          channelIds,
        ),
      );
    }

    // Only send message if isFleetTrackingWindowOpen. Note: this will always
    // be false when we're in the FleetTracking window
    if (useOperationsStore().operationOptions.jobListWindowOpen) {
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.JOB_LIST,
        new BroadcastMessage(
          BroadcastIds.JOB_LIST.TO_EXTERNAL.OPERATIONS_CHANNEL_UPDATED,
          channelIds,
        ),
      );
    }
  }

  /**
   * Saves or updates an OperationsChannel over websocket and listens for the response.
   * Returns the saved OperationsChannel if successful, otherwise null.
   * @param channel - The OperationsChannel to save or update
   * @returns OperationsChannel | null
   */
  async function saveOperationsChannel(
    channel: OperationsChannel,
  ): Promise<OperationsChannel | null> {
    try {
      channel.company ||= sessionManager.getCompanyId();
      channel.division ||= sessionManager.getDivisionId();

      const result: OperationsChannel | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest('/operationsChannel/save', channel, true),
          'savedOperationsChannel',
        );
      return result ? new OperationsChannel(result) : null;
    } catch (error) {
      logConsoleError('Error saving OperationsChannel', error);
      return null;
    }
  }

  /**
   * Handler for `savedOperationsChannel` event, called from global event
   * listeners. This updates the state with the updated document. Called when
   * the user updates their owned channel, or if another user triggers an update
   * on a division-level channel document.
   * @param channel - The OperationsChannel to update state with
   */
  async function handleUpdatedOperationsChannel(
    channel: OperationsChannel | null,
  ): Promise<void> {
    if (!channel?._id) {
      logConsoleError(
        'handleUpdatedOperationsChannel called with null channel',
      );
      return;
    }
    channel = new OperationsChannel(channel);
    // Update the existing channel in the store
    const existingChannelIndex = operationsChannels.value.findIndex(
      (ch) => ch._id === channel._id,
    );
    if (existingChannelIndex !== -1) {
      operationsChannels.value.splice(existingChannelIndex, 1, channel);
    } else {
      operationsChannels.value.push(channel);
    }

    // Check if channel._id is contained in the list of currently selected
    // channels. If so, refresh the selected channel object so we have
    // up-to-date filter information
    if (selectedOperationsChannelIds.value?.includes(channel._id!)) {
      refreshSelectedOperationsChannel();
    }
  }

  /**
   * Deletes an OperationsChannel over websocket and listens for the response.
   * Returns the deleted OperationsChannel if successful, otherwise null.
   * @param channel - The OperationsChannel to delete
   * @returns OperationsChannel | null
   */
  async function deleteOperationsChannel(
    channel: OperationsChannel,
  ): Promise<OperationsChannel | null> {
    try {
      const result: OperationsChannel | string | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest('/operationsChannel/delete', channel, true),
          'deletedOperationsChannel',
        );
      // If result is a string, it indicates an error message
      if (typeof result === 'string') {
        throw new Error(result);
      }
      return result ? new OperationsChannel(result) : null;
    } catch (error) {
      logConsoleError('Error deleting OperationsChannel', error);
      return null;
    }
  }

  /**
   * Handler for `deletedOperationsChannel` event, called from global event
   * listeners. This updates the state by removing the deleted channel.
   * Called when the user deletes their owned channel, or if another user triggers
   * a deletion on a division-level channel document.
   * @param channel - The OperationsChannel that was deleted
   */
  async function handleDeletedOperationsChannel(
    channel: OperationsChannel | string | null,
  ): Promise<void> {
    // This case shouldn't happen, as the string response happens before the
    // message is processed by pro-core-data. Aka a division-level response
    // should never happen with string type
    if (typeof channel === 'string') {
      return;
    }
    if (!channel?._id) {
      logConsoleError(
        'handleDeletedOperationsChannel called with null channel',
      );
      return;
    }
    // Remove the channel from the store
    const index = operationsChannels.value.findIndex(
      (ch) => ch._id === channel._id,
    );
    if (index !== -1) {
      operationsChannels.value.splice(index, 1);
    }

    // If the deleted channel was selected, update the selection
    if (selectedOperationsChannelIds.value?.includes(channel._id)) {
      // Remove the deleted channel from the selected IDs
      selectedOperationsChannelIds.value =
        selectedOperationsChannelIds.value.filter((id) => id !== channel._id);
      if (!selectedOperationsChannelIds.value.length) {
        selectedOperationsChannelIds.value = null;
        localStorage.removeItem(LOCAL_STORAGE_SELECTED_OPERATIONS_CHANNELS);
      } else {
        localStorage.setItem(
          LOCAL_STORAGE_SELECTED_OPERATIONS_CHANNELS,
          JSON.stringify(selectedOperationsChannelIds.value),
        );
      }
      refreshSelectedOperationsChannel();
    }
  }

  /**
   * Refreshes the currently selected operations channel based on the selected
   * channel IDs.
   *
   * - If exactly one channel is selected, sets `selectedOperationsChannel` to
   *   that channel.
   * - If multiple channels are selected, combines them into a single filter
   *   using `combineOperationsChannels`.
   * - If no channels are selected, clears the selection.
   * - If a selected channel is not found, clears the selection and removes the
   *   persisted selection from local storage.
   *
   * Called after the initial OperationsChannel list arrives to frontend, when
   * the user updates their channel selection, or when the frontend receives an
   * update for a channel currently being used.
   */
  function refreshSelectedOperationsChannel() {
    const channelIds = selectedOperationsChannelIds.value ?? [];
    // If there's only channel selected, then apply it directly
    if (channelIds.length === 1) {
      const foundChannel = operationsChannels.value.find(
        (channel) => channel._id === channelIds[0],
      );
      if (foundChannel) {
        selectedOperationsChannel.value = foundChannel;
      } else {
        selectedOperationsChannelIds.value = null;
        localStorage.removeItem(LOCAL_STORAGE_SELECTED_OPERATIONS_CHANNELS);

        // Reset selection to default
        resetSelectedOperationsChannelToDefault();
      }
    } else if (channelIds.length > 1) {
      // If there's more than one, all OperationsChannels into a single combined filter
      selectedOperationsChannel.value = combineOperationsChannels(
        channelIds
          .map((id) =>
            operationsChannels.value.find((channel) => channel._id === id),
          )
          .filter(
            (channel): channel is OperationsChannel => channel !== undefined,
          ),
      );
    } else {
      // If there's none, clear the selection
      resetSelectedOperationsChannelToDefault();
    }
  }

  function resetSelectedOperationsChannelToDefault() {
    // Use the default channel if no specific channel is selected (if one exists). If none exist, use the stock one
    const foundDefaultChannel = operationsChannels.value.find(
      (channel) => channel.isDefaultChannel,
    );
    selectedOperationsChannel.value =
      foundDefaultChannel ?? returnCleanDefaultChannel();
  }

  /**
   * Initializes the list of operation channels by fetching all available
   * channels asynchronously and updating the `operationsChannels` state if the
   * fetch is successful.
   *
   * @returns {Promise<void>} A promise that resolves when the operation
   * channels list has been initialized.
   */
  async function initOperationsChannelsList(): Promise<void> {
    const channels = await getAllOperationsChannels();
    if (channels) {
      setOperationsChannels(channels);
    }
  }

  /**
   * Sets the list of available operations channels and updates the selection state.
   *
   * @param channels - An array of `OperationsChannel` objects to set as the available channels.
   *
   * If there are any selected channel IDs present, this function will also refresh the selected operations channel
   * to ensure the filter state is consistent with the new list of channels.
   */
  function setOperationsChannels(channels: OperationsChannel[]) {
    // If incoming channels aren't instance of OperationsChannel, init
    operationsChannels.value = channels.map((ch) =>
      ch instanceof OperationsChannel ? ch : new OperationsChannel(ch),
    );

    // Refresh the selected channel based on response
    refreshSelectedOperationsChannel();
  }

  /**
   * Retrieves all OperationsChannels over websocket and listens for the response.
   * Returns an array of OperationsChannel if successful, otherwise null.
   * @returns OperationsChannel[] | null
   */
  async function getAllOperationsChannels(): Promise<
    OperationsChannel[] | null
  > {
    try {
      const result: OperationsChannel[] | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest('/operationsChannel/getAll', undefined, true),
          'getAllOperationsChannels',
        );
      return result ? result.map((item) => new OperationsChannel(item)) : null;
    } catch (error) {
      logConsoleError('Error retrieving all OperationsChannels', error);
      return null;
    }
  }

  return {
    operationsChannels,
    selectedOperationsChannelIds,
    isOperationsChannelFilterApplied,
    selectedOperationsChannel,
    clearDriverFilters,
    clearJobFilters,
    saveOperationsChannel,
    deleteOperationsChannel,
    getAllOperationsChannels,
    initOperationsChannelsList,
    setOperationsChannels,
    handleUpdatedOperationsChannel,
    handleDeletedOperationsChannel,
    setSelectedOperationsChannelIds,
  };
});
