import {
  addPropertiesToDriverMessageSettings,
  LOCAL_STORAGE_DASHBOARD_SETTINGS,
  LOCAL_STORAGE_JOB_LIST_GROUPING,
  LOCAL_STORAGE_POPOUT_JOB_LIST_GROUPING,
  LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS,
} from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  BroadcastChannelType,
  BroadcastIds,
  returnOperationsBroadcastChannelId,
} from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { addOrReplaceRateTableItems } from '@/helpers/RateHelpers/ServiceRateHelpers';
import { requestZoneToZoneRatesForPudItems } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import AddNoteToJobRequest from '@/interface-models/Generic/Communication/AddNoteToJobRequest';
import DateRange from '@/interface-models/Generic/DateRange/DateRange';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { JobListStatusGrouping } from '@/interface-models/Generic/OperationScreenOptions/JobListStatusGrouping';
import { OperationScreenOptions } from '@/interface-models/Generic/OperationScreenOptions/OperationScreenOptions';
import OperationsDashboardComponentSettings, {
  dashboardSettingsDefault,
} from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardComponentSettings';
import OperationsDashboardSetting from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import AllocateJobRequest from '@/interface-models/Jobs/Allocation/AllocateJobRequest';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import {
  BookJobWithUnassignedPudConfig,
  JobBookingPageConfig,
  PudMaintenanceDialogConfig,
} from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceDialogConfig';
import { FleetAssetServiceRateResponse } from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRateResponse';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { AllocationRateRequest } from '@/interface-models/ServiceRates/RateRequest';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import Mitt from '@/utils/mitt';
import moment from 'moment-timezone';
import { defineStore } from 'pinia';
import { computed, ComputedRef, Ref, ref } from 'vue';

enum BookingScreenType {
  LEGACY,
  NEW,
}

export const useOperationsStore = defineStore('useOperationsStore', () => {
  const selectedFleetAssetId: Ref<string> = ref('');
  const selectedDriverId: Ref<string> = ref('');
  const selectedJobId: Ref<number> = ref(-1);
  const operationOptions: Ref<OperationScreenOptions> = ref(
    new OperationScreenOptions(),
  );
  const selectedBookingScreenJobId: Ref<number> = ref(0);
  const editingCommunicationInDialog: Ref<AddNoteToJobRequest | null> =
    ref(null);
  const viewingJobNotesDialog: Ref<boolean> = ref(false);
  const viewingJobDetailsDialog: Ref<boolean> = ref(false);
  const viewingOutsideHireDetailsDialog: Ref<boolean> = ref(false);
  const viewingAssetInformationDialog: Ref<boolean> = ref(false);
  const viewingPudMaintenanceDialog: Ref<boolean> = ref(false);
  const viewingPudSearchDialog: Ref<boolean> = ref(false);
  const pudMaintenanceDialogConfig: Ref<PudMaintenanceDialogConfig | null> =
    ref(null);
  const bookJobWithUnassignedPudConfig: Ref<BookJobWithUnassignedPudConfig | null> =
    ref(null);
  const jobListDateFilter: Ref<DateRange | null> = ref(null);
  const isJobServiceFailure: Ref<boolean> = ref(false);
  const isJobCancellation: Ref<boolean> = ref(false);
  const isJobReleaseForEditing: Ref<boolean> = ref(false);
  const isRestoreCancelledJob: Ref<boolean> = ref(false);
  const isDispatchNote: Ref<boolean> = ref(false);
  const isStartOfDayCheckNote: Ref<boolean> = ref(false);
  const releaseForEditingRequestMade: Ref<boolean> = ref(false);
  const reloadJobInBookingScreen: Ref<number> = ref(0);
  const reloadUnassignedPudJobInBookingScreen: Ref<number> = ref(0);
  const selectedFleetAssetDialogTab: Ref<string> = ref('ASSET_DETAILS');
  const selectedJobDetailsDialogTab: Ref<string> = ref('SUM');
  const selectedJobDetails: Ref<JobDetails | null> = ref(null);
  const dashboardSettings: Ref<OperationsDashboardComponentSettings> = ref(
    dashboardSettingsDefault,
  );
  const selectedJobListStatusGrouping: Ref<JobListStatusGrouping> = ref(
    JobListStatusGrouping.UNALLOCATED,
  );
  const operationsBroadcastId: Ref<string> = ref('');
  const reBookJobBookingPageConfig: Ref<JobBookingPageConfig | null> =
    ref(null);

  /**
   * Resets all state variables back to their default values. Called when
   * logging out or changing divisions
   */
  function resetState() {
    selectedFleetAssetId.value = '';
    selectedDriverId.value = '';
    selectedJobId.value = -1;
    operationOptions.value = new OperationScreenOptions();
    selectedBookingScreenJobId.value = 0;
    editingCommunicationInDialog.value = null;
    viewingJobNotesDialog.value = false;
    viewingJobDetailsDialog.value = false;
    viewingOutsideHireDetailsDialog.value = false;
    viewingAssetInformationDialog.value = false;
    viewingPudMaintenanceDialog.value = false;
    viewingPudSearchDialog.value = false;
    pudMaintenanceDialogConfig.value = null;
    bookJobWithUnassignedPudConfig.value = null;
    jobListDateFilter.value = null;
    isJobServiceFailure.value = false;
    isJobCancellation.value = false;
    isJobReleaseForEditing.value = false;
    isRestoreCancelledJob.value = false;
    isDispatchNote.value = false;
    isStartOfDayCheckNote.value = false;
    releaseForEditingRequestMade.value = false;
    reloadJobInBookingScreen.value = 0;
    reloadUnassignedPudJobInBookingScreen.value = 0;
    selectedFleetAssetDialogTab.value = 'ASSET_DETAILS';
    selectedJobDetailsDialogTab.value = 'SUM';
    selectedJobDetails.value = null;
    dashboardSettings.value = dashboardSettingsDefault;
    selectedJobListStatusGrouping.value = JobListStatusGrouping.UNALLOCATED;
    operationsBroadcastId.value = '';
  }

  /**
   * Sets the value of the jobListWindowOpen variable, which determines whether
   * the job list window is open or not. Checked when certain updates come in so
   * we know if we need to forward it
   * @param value - boolean value to set jobListWindowOpen
   */
  function setJobListWindow(value: boolean) {
    operationOptions.value.jobListWindowOpen = value;
  }

  /**
   * Sets the value of the reBookJobBookingPageConfig variable,
   * which determines rebook job details.
   * @param value - JobBookingPageConfig to set reBookJobBookingPageConfig
   */
  function setReBookJobBookingPageConfig(value: JobBookingPageConfig) {
    reBookJobBookingPageConfig.value = value;
  }

  /**
   * Sets the selected job ID to the provided value. If the value is different
   * to the currently selected job ID, then close all job-dependant dialog
   * @param jobId - The ID of the job to be selected
   */
  function setSelectedJobId(jobId: number) {
    // If we're choosing a different job to the currently selected jobId, then
    // close all job-dependant dialog
    if (jobId !== selectedJobId.value) {
      viewingJobNotesDialog.value = false;
      viewingJobDetailsDialog.value = false;
      viewingOutsideHireDetailsDialog.value = false;
    }
    selectedJobId.value = jobId;
  }

  /**
   *
   * @param date
   */
  function setJobListDateFilter(date: number) {
    const userTimeZone =
      useCompanyDetailsStore().userLocale ?? moment.tz.guess();

    jobListDateFilter.value = new DateRange(
      moment(date).tz(userTimeZone).startOf('day').valueOf(),
      moment(date).tz(userTimeZone).endOf('day').valueOf(),
    );
  }

  /**
   * Sets the value of the releaseForEditingRequestMade variable, which is called from the  used
   * in the JobDetailsDialog to determine if we need to jump to the PRICING tab.
   * @param value - the boolean value to set releaseForEditingRequestMade
   */
  function setReleaseForEditingRequestMade(value: boolean) {
    releaseForEditingRequestMade.value = value;
  }

  /**
   * Sets the selected fleet asset id for the Operations components. Used to
   * show which fleet asset and driver combination is being focused on the
   * dashboard.
   * @param fleetAssetId The ID of the fleet asset to be selected.
   */
  function setSelectedFleetAssetId(fleetAssetId: string) {
    selectedFleetAssetId.value = fleetAssetId;
    const gpsData = useGpsStore().allGpsPositions.get(fleetAssetId);
    if (gpsData) {
      useFleetMapStore().setJumpToLocation([
        gpsData.longitude,
        gpsData.latitude,
      ]);
    }
  }

  /**
   * Sets the selected driver id for the Operations components. Used to show
   * which fleet asset and driver combination is being focused on the dashboard.
   * @param driverId The ID of the driver to be selected.
   */
  function setSelectedDriverId(driverId: string) {
    selectedDriverId.value = driverId;
  }

  function bookNewJob() {
    useAppNavigationStore().setCurrentComponentId('#job-booking');
    Mitt.emit('bookJobWithConfig', {
      jobId: -1,
      operationType: JobOperationType.EDIT,
    });
  }

  // ===========================================================================
  // Selected JOB IDs for Operations Components
  // ===========================================================================
  /**
   * Used to determine what job is currently selected in the booking screen for
   * editing.
   * @param jobId - The ID of the job to be edited
   */
  function setSelectedBookingScreenJobId(jobId: number) {
    if (bookingScreenType.value === BookingScreenType.NEW) {
      useAppNavigationStore().setCurrentComponentId('#job-booking');
      Mitt.emit('bookJobWithConfig', {
        jobId: jobId,
        operationType: JobOperationType.EDIT,
      });
    } else {
      if (selectedBookingScreenJobId.value === jobId) {
        reloadJobInBookingScreen.value++;
      }
      selectedBookingScreenJobId.value = jobId;
    }
  }

  const bookingScreenType: ComputedRef<BookingScreenType> = computed(() => {
    // return BookingScreenType.LEGACY;
    return BookingScreenType.NEW;
  });

  /**
   * The selected tab for the job details dialog. This is used to jump to a
   * particular tab in the job details dialog.
   * @param tabId - The ID of the tab to be selected.
   */
  function setSelectedJobDetailsDialogTab(tabId: string = '') {
    if (!tabId) {
      selectedJobDetailsDialogTab.value = 'SUM';
      return;
    }

    selectedJobDetailsDialogTab.value = tabId;
  }

  /**
   * Sets the selected tab for the fleet asset dialog, such that we can open the
   * Fleet Asset Dialog at a specified tab. If no tabId is provided, the default
   * tab is 'ASSET_DETAILS'.
   * @param tabId The ID of the tab to be selected.
   */
  function setSelectedFleetAssetDialogTab(tabId: string = '') {
    if (!tabId) {
      selectedFleetAssetDialogTab.value = 'ASSET_DETAILS';
      return;
    }
    selectedFleetAssetDialogTab.value = tabId;
  }

  /**
   * Controls visibility of the Job Notes dialog. If value is true, the dialog
   * will be shown. If value is false, the dialog will be hidden.
   * @param value - The boolean value to set viewingJobNotesDialog
   */
  function setViewingJobNotesDialog(value: boolean) {
    viewingJobNotesDialog.value = value;
  }

  /**
   * Controls visibility of the Outside Hire Details dialog. If value is true,
   * the dialog will be shown. If value is false, the dialog will be hidden.
   * @param value - The boolean value to set viewingOutsideHireDetailsDialog
   */
  function setViewingOutsideHireDetailsDialog(value: boolean) {
    viewingOutsideHireDetailsDialog.value = value;
  }

  /**
   * Opens the note dialog with a not to be edited in the note dialog.
   * @param request - The request to be edited in the note dialog.
   */
  function setEditingCommunicationInDialog(
    request: AddNoteToJobRequest | null,
  ) {
    editingCommunicationInDialog.value = request;
  }

  /**
   * In the note dialog, sets the boolean that determines whether or not service
   * failure update needs to be sent afterwards as well.
   * @param value - The boolean value to set isJobServiceFailure
   */
  function setJobServiceFailure(value: boolean) {
    isJobServiceFailure.value = value;
  }

  /**
   * Used in note dialog to determine whether the job needs to be released for
   * editing when we submit the note request.
   * @param value - The boolean value to set isJobReleaseForEditing
   */
  function setJobReleaseForEditing(value: boolean) {
    isJobReleaseForEditing.value = value;
  }

  /**
   * In the note dialog, sets the boolean that determines whether or not the job
   * needs to be cancelled while submitting the note.
   * @param value - The boolean value to set isJobCancellation
   */
  function setJobCancellation(value: boolean) {
    isJobCancellation.value = value;
  }

  /**
   * In the note dialog, sets the boolean that determines whether or not the job
   * needs to be restored while submitting the note.
   * @param value - The boolean value to set isRestoreCancelledJob
   */
  function setRestoreCancelledJob(value: boolean) {
    isRestoreCancelledJob.value = value;
  }

  /**
   * In the note dialog, sets the boolean that determines whether the note being
   * created is a dispatch note.
   * @param value - The boolean value to set isDispatchNote
   */
  function setDispatchNote(value: boolean) {
    isDispatchNote.value = value;
  }

  /**
   * In the note dialog, sets the boolean that determines whether the note being
   * created is a start of day check note.
   * @param value - The boolean value to set isStartOfDayCheckNote
   */
  function setStartOfDayCheckNote(value: boolean) {
    isStartOfDayCheckNote.value = value;
  }

  /**
   * Controls the visibility of the JobDetailsDialog. If value is true, the
   * dialog will be shown. If value is false, the dialog will be hidden.
   * @param value - The boolean value to set viewingJobDetailsDialog
   */
  function setViewingJobDetailsDialog(value: boolean) {
    viewingJobDetailsDialog.value = value;
    // Close other dialogs if opening the JobDetails dialog.
    if (value) {
      viewingJobNotesDialog.value = false;
      viewingAssetInformationDialog.value = false;
    }
  }

  // Flow for PudMaintenanceDialog:
  // 1. Construct config based on requirements (see [PudMaintenanceDialogConfig] model for options)
  // 2. BEFORE opening dialog, call mutation [setPudMaintenanceDialogConfig] with config
  // 3. Call [setViewingPudMaintenanceDialog] and set to true
  // 4. Dialog component will pull in the desired config on mount
  function setViewingPudMaintenanceDialog(value: boolean) {
    viewingPudMaintenanceDialog.value = value;
    if (!value) {
      pudMaintenanceDialogConfig.value = null;
    }
  }

  /**
   * Controls the visibility of the UnassignedPudSearch dialog. Opened from the
   * OperationsActionButtons.
   * @param value - The boolean value to set viewingPudSearchDialog
   */
  function setViewingPudSearchDialog(value: boolean) {
    viewingPudSearchDialog.value = value;
  }

  /**
   * Sets the configuration for the PudMaintenanceDialog, which contains
   * settings to control what is displayed in the dialog.
   * @param config - The configuration for the PudMaintenanceDialog
   */
  function setPudMaintenanceDialogConfig(config: PudMaintenanceDialogConfig) {
    if (config) {
      pudMaintenanceDialogConfig.value = config;
    }
  }

  /**
   * Sets configuration to book a job with an unassigned PUD item. This is used
   * to pass the necessary information to the booking screen to book a job with
   * UnassignedPudItems.
   * @param config - The configuration for the booking screen
   */
  function setBookJobWithUnassignedPudConfig(
    config: BookJobWithUnassignedPudConfig | null,
  ) {
    // If we're using the new booking screen, then emit a mitt event and jump to the new booking screen
    if (bookingScreenType.value === BookingScreenType.NEW && config) {
      // Make sure we have a clientId
      if (!config.clientId) {
        console.error(
          'Client ID is required to book a job with unassigned PUDs',
          config,
        );
        return;
      }
      const { operationType, jobId, unassignedPudIds, clientId } = config;
      const appNavigationStore = useAppNavigationStore();
      const emitConfig = {
        jobId: jobId || -1,
        operationType,
        unassignedPudIds,
        clientId,
      };
      if (operationType === JobOperationType.NEW || !jobId) {
        emitConfig.operationType = JobOperationType.NEW;
      }
      appNavigationStore.setCurrentComponentId('#job-booking');
      Mitt.emit('bookJobWithConfig', emitConfig);
    } else {
      bookJobWithUnassignedPudConfig.value = config;
      reloadUnassignedPudJobInBookingScreen.value++;
      if (!config) {
        return;
      }
    }
  }

  // -------------------------------------------------------

  /**
   * Controls visibility of the AssetInformationDialog. If value is true, the
   * dialog will be shown. If value is false, the dialog will be hidden.
   * @param value - The boolean value to set viewingAssetInformationDialog
   */
  function setViewingAssetInformationDialog(value: boolean) {
    viewingAssetInformationDialog.value = value;
    // if (!value) {
    //   selectedJobId.value = -1;
    // }

    // Hide other dialogs
    if (value) {
      viewingJobNotesDialog.value = false;
      viewingJobDetailsDialog.value = false;
    }
  }

  /**
   * Fetches the full job details for the selected job ID and returns it. Unlike
   * getFullJobDetails, this does NOT set the response to state.
   * @param jobId - The ID of the job to fetch details for
   */
  async function requestJobDetailsByJobId(
    jobId: number,
  ): Promise<JobDetails | null> {
    try {
      if (!jobId) {
        return null;
      }
      // Send request over websocket
      let result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/job/getJobByJobId', jobId, false),
        'selectedJobByJobId',
      );
      if (result) {
        result = initialiseJobDetails(result);
        result.addAdditionalJobData();
        result.getRouteProgressMatrix();
      }
      return result;
    } catch (error) {
      console.error('Error retrieving job details: ', error);
      return null;
    }
  }

  /**
   * Fetches the full job details for the selected job ID, and sets it to the
   * selectedJobDetails property for use in the Operations Dashboard and
   * JobDetailsDialog.
   * @param jobId - The ID of the job to fetch details for
   * @returns A boolean indicating whether the request was successful
   */
  async function getFullJobDetails(jobId: number): Promise<boolean> {
    try {
      // Send request over websocket
      const result = await requestJobDetailsByJobId(jobId);
      if (result) {
        setFullJobDetails(result);
        return true;
      } else {
        showNotification(GENERIC_ERROR_MESSAGE);
        return false;
      }
    } catch (error) {
      console.error('Error retrieving job details: ', error);
      return false;
    }
  }

  /**
   * Handles response to requested called in getFullJobDetails. Initialises the job and the object to selectedJobDetails.
   * @param jobDetails
   */
  function setFullJobDetails(jobDetails: JobDetails) {
    selectedJobDetails.value = jobDetails;

    // Send jobDetails to the window
    if (operationOptions.value.jobListWindowOpen) {
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.JOB_LIST,
        new BroadcastMessage('selectedJobDetails', selectedJobDetails.value),
      );
    }
  }

  /**
   * Sends the updated job summary to the job list window if it's opens.
   * @param operationJobSummary - The updated job summary to be sent to the job
   * list window
   */
  function publishUpdatedJobToWindow(operationJobSummary: OperationJobSummary) {
    if (
      !operationJobSummary.jobId ||
      !operationOptions.value.jobListWindowOpen
    ) {
      return;
    }
    // Send updated job summary to window
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      new BroadcastMessage('updatedJobDetails', operationJobSummary),
    );
  }

  /**
   * Receive the request from the Popout Window for service rates. Request the
   * rates and send the response back to the popout window
   * @param rateRequest - The rate request to be sent to the window
   */
  async function getActiveFleetAssetServiceRatesFromWindow(
    rateRequest: AllocationRateRequest,
  ) {
    const rateResponse =
      await getFleetAssetServiceRatesForAllocation(rateRequest);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      new BroadcastMessage(
        BroadcastIds.JOB_LIST.TO_EXTERNAL.GET_FLEET_RATES_FROM_WINDOW_RESPONSE,
        rateResponse ?? null,
      ),
    );
  }

  /**
   * Called via BroadcastChannel when the popout window requests to pre-allocate
   * a job. This method sends the request to the AllocationStore to pre-allocate
   * the job, then forwards the response back to the popout window.
   *
   * @param request - The request to pre-allocate a job, containing the
   * preallocation details (jobId, fleetAssetId, driverId, etc.)
   */
  async function sendPreAllocationRequestFromWindow(
    request: AllocateJobRequest,
  ): Promise<void> {
    const response = await useAllocationStore().preAllocateJob(request);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      new BroadcastMessage(
        BroadcastIds.JOB_LIST.TO_EXTERNAL.PREALLOCATE_FROM_WINDOW_RESPONSE,
        response,
      ),
    );
  }

  /**
   * Called via BroadcastChannel when the popout window requests to add a note
   * to a job. This method sends the request to the backend to add the note,
   * then forwards the response back to the popout window.
   * @param request - The request to add a note to a job
   */
  async function sendAddNoteToJobRequestFromWindow(
    request: AddNoteToJobRequest,
  ): Promise<void> {
    const response = await useJobStore().addNoteToJob(request);
    // If the request was successful, we should update the job details
    // in the store and send the updated job details to the window
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      new BroadcastMessage(
        BroadcastIds.JOB_LIST.TO_EXTERNAL.SEND_ADD_NOTE_RESPONSE,
        response,
      ),
    );
  }

  /**
   * Used to fetch the service rates for the fleet asset from the AllocateDriver
   * component. This method first fetches the service rates (as usual), then
   * checks if the selected job is a zone to zone rate job. If it is, it
   * requests the zone to zone rates for the job so we can verify that the fleet
   * asset has rates available for the suburbs.
   * @param rateRequest - The rate request containing fleetAssetId, searchDate
   * and the jobId to check for zone to zone.
   * @returns - The response containing the service rates for the fleet asset.
   */
  async function getFleetAssetServiceRatesForAllocation(
    rateRequest: AllocationRateRequest,
  ): Promise<FleetAssetServiceRateResponse | null> {
    try {
      const rateResponse =
        await useServiceRateStore().getMergedFleetAssetServiceRates(
          rateRequest.entityId,
          rateRequest.searchDate,
        );

      // Check if the selected job is a zone to zone rate job
      if (
        rateRequest.jobId &&
        rateResponse?.fleetAssetServiceRate &&
        selectedJobDetails.value?.jobId === rateRequest.jobId &&
        selectedJobDetails.value?.rateTypeId === JobRateType.ZONE_TO_ZONE
      ) {
        // If it is, then we should request the zone to zone rates for the job and
        // add the resulting rate table items tot eh rate response
        if (selectedJobDetails.value.pudItems.length >= 2) {
          const zzRateTableItems = await requestZoneToZoneRatesForPudItems({
            type: RateEntityType.FLEET_ASSET,
            entityId: rateRequest.entityId,
            pudItems: selectedJobDetails.value.pudItems,
            serviceTypeId: selectedJobDetails.value.serviceTypeId,
          });
          // Add the rate table items in the ztz response to the merged service
          // rate response
          if (zzRateTableItems && zzRateTableItems.length) {
            // Set rate type object to empty string to avoid mismatched zones in
            // the pricing screen (in case more stops are added)
            zzRateTableItems.forEach((item) => {
              // TODO: This is a temporary solution until we update fleet asset
              // rates from the booking screen.
              item.rateTypeObject = '' as any;
            });
            addOrReplaceRateTableItems(
              rateResponse.fleetAssetServiceRate.rateTableItems,
              zzRateTableItems,
            );
          }
        }
      }
      return rateResponse;
    } catch (error) {
      logConsoleError(
        'Error occurred while allocating fleet asset to job.',
        rateRequest,
        error,
      );
      return null;
    }
  }

  /**
   * Saves the incoming settings to the dashboard settings for the job list
   * component. Also saves the settings to local storage.
   * @param updatedSettings - The updated settings to be saved
   */
  function updateDashboardSettingsJobList(
    updatedSettings: OperationsDashboardSetting[],
  ) {
    dashboardSettings.value.jobList = updatedSettings;
    // Update settings in browser local storage. Store value with different key
    // to allow different settings between dashboard and popout job list
    const isJobListPopout =
      useAppNavigationStore().currentRouteTitle === 'job_list';
    localStorage.setItem(
      isJobListPopout
        ? LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS
        : LOCAL_STORAGE_DASHBOARD_SETTINGS,
      JSON.stringify(dashboardSettings.value),
    );
  }

  /**
   * Saves the incoming settings to the dashboard settings for the allocated work
   * component. Also saves the settings to local storage.
   * @param updatedSettings - The updated settings to be saved
   */
  function updateDashboardSettingsAllocatedWork(
    updatedSettings: OperationsDashboardSetting[],
  ) {
    dashboardSettings.value.allocatedWork = updatedSettings;
    // Update settings in browser local storage. Store value with different key to
    // allow different settings between dashboard and popout job list
    const isJobListPopout =
      useAppNavigationStore().currentRouteTitle === 'job_list';
    localStorage.setItem(
      isJobListPopout
        ? LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS
        : LOCAL_STORAGE_DASHBOARD_SETTINGS,
      JSON.stringify(dashboardSettings.value),
    );
  }

  /**
   * Saves the incoming settings to the dashboard settings for the job information
   * component. Also saves the settings to local storage.
   * @param updatedSettings - The updated settings to be saved
   */
  function updateDashboardSettingsJobInformation(
    updatedSettings: OperationsDashboardSetting[],
  ) {
    dashboardSettings.value.jobInformation = updatedSettings;
    // Update settings in browser local storage. Store value with different key
    // to allow different settings between dashboard and popout job list
    const isJobListPopout =
      useAppNavigationStore().currentRouteTitle === 'job_list';
    localStorage.setItem(
      isJobListPopout
        ? LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS
        : LOCAL_STORAGE_DASHBOARD_SETTINGS,
      JSON.stringify(dashboardSettings.value),
    );
  }

  /**
   * Saves the incoming settings to the dashboard settings for the fleet asset list
   * component. Also saves the settings to local storage.
   * @param updatedSettings - The updated settings to be saved
   */
  function updateDashboardSettingsFleetAssetList(
    updatedSettings: OperationsDashboardSetting[],
  ) {
    dashboardSettings.value.fleetAssetList = updatedSettings;
    // Update settings in browser local storage. Store value with different key
    // to allow different settings between dashboard and popout job list
    const isJobListPopout =
      useAppNavigationStore().currentRouteTitle === 'job_list';
    localStorage.setItem(
      isJobListPopout
        ? LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS
        : LOCAL_STORAGE_DASHBOARD_SETTINGS,
      JSON.stringify(dashboardSettings.value),
    );
  }

  /**
   * Saves the incoming settings to the dashboard settings for the driver message
   * component. Also saves the settings to local storage.
   * @param updatedSettings - The updated settings to be saved
   */
  function updateDashboardSettingsDriverMessage(
    updatedSettings: OperationsDashboardSetting[],
  ) {
    dashboardSettings.value.messageList =
      addPropertiesToDriverMessageSettings(updatedSettings);
    // Update settings in browser local storage. Store value with different key
    // to allow different settings between dashboard and popout job list
    const isJobListPopout =
      useAppNavigationStore().currentRouteTitle === 'job_list';
    localStorage.setItem(
      isJobListPopout
        ? LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS
        : LOCAL_STORAGE_DASHBOARD_SETTINGS,
      JSON.stringify(dashboardSettings.value),
    );
  }

  /**
   * Saves the incoming settings to the dashboard settings for the job list
   * grouping. Also saves the settings to local storage.
   * @param updatedSettings - The updated settings to be saved
   */
  function updateJobListStatusGrouping(updatedGrouping: JobListStatusGrouping) {
    selectedJobListStatusGrouping.value = updatedGrouping;
    // Update grouping in browser local storage. Store value with different key
    // to allow different settings between dashboard and popout job list
    const isJobListPopout =
      useAppNavigationStore().currentRouteTitle === 'job_list';
    localStorage.setItem(
      isJobListPopout
        ? LOCAL_STORAGE_POPOUT_JOB_LIST_GROUPING
        : LOCAL_STORAGE_JOB_LIST_GROUPING,
      updatedGrouping,
    );
  }

  /**
   * Checks if a message list setting (stored in the browsers local storage and
   * also in store) is active for the provided settingId.
   *
   * @param settingId - The ID of the setting to check.
   * @param driverId - Optional. The ID of the driver to exclude from the check.
   * @returns A boolean indicating whether the setting is active.
   */
  function isMessageListSettingActive(
    settingId: string,
    driverId?: string,
  ): boolean {
    const settings: OperationsDashboardSetting[] =
      useOperationsStore().dashboardSettings.messageList;
    const foundSetting = settings.find((s) => s.id === settingId);
    if (!foundSetting) {
      return false;
    }
    // Return true if the setting is active and (if driverId is provided) that the
    // driverId is not excluded from this particular setting
    const result =
      foundSetting.active &&
      (!driverId ||
        !foundSetting.excludeIds ||
        !foundSetting.excludeIds.includes(driverId));

    return result;
  }

  /**
   * Creates a broadcast channel for the operations components. This is used to
   * communicate between the main window and the popout windows.
   * @param route - The current route of the main window
   */
  function createBroadcastChannel(route: string | null | undefined) {
    // Set operationsBroadcastId using values from token to form unique channel
    // id Construct id and commit to store
    const broadcastId = returnOperationsBroadcastChannelId(
      sessionManager.getUserName(),
      sessionManager.getJtiSessionId(),
    );
    useBroadcastChannelStore().setOperationsBroadcastId(broadcastId);

    // Create broadcast channel
    const broadcast: BroadcastChannel =
      useBroadcastChannelStore().initBroadcastChannel(
        BroadcastChannelType.JOB_LIST,
      );
    broadcast.onmessage = (message: any) => {
      console.log(
        `OperationsStore broadcast channel - received message id '${message.data.id}'`,
      );
      const jobStore = useJobStore();
      const allocationStore = useAllocationStore();

      switch (message.data.id) {
        // PROCESS -----
        // 1. The window is opened
        // 2. Window sends a JOB_LIST_WINDOW_OPEN message back to the main
        //    window
        // 3. Main window (this) receives the message and sets the
        //    jobListWindowOpen
        // 4. Main window sends all the necessary key data the popout window
        //    needs to operate
        // 5. The popout window handles these messages in the job list component
        //    (jobList.ts)
        case BroadcastIds.JOB_LIST.TO_MAIN.JOB_LIST_WINDOW_OPEN:
          const windowOpen = message.data.value;
          setJobListWindow(windowOpen);
          if (windowOpen) {
            const fleetAssetStore = useFleetAssetStore();
            const fleetAssetList = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.FLEET_ASSET_LIST,
              fleetAssetStore.getAllFleetAssetList.filter(
                (fa) => fa.isActiveForAllocation,
              ),
            );
            broadcast.postMessage(fleetAssetList);
            const allDrivers = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.ALL_DRIVERS,
              useDriverDetailsStore().getDriverList,
            );
            broadcast.postMessage(allDrivers);
            const allClients = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.CLIENT_LIST,
              useClientDetailsStore().clientSummaryList,
            );
            broadcast.postMessage(allClients);
            const statusTypeList = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.STATUS_TYPE_LIST,
              useRootStore().statusTypeList,
            );
            broadcast.postMessage(statusTypeList);
            // DIVISION DETAILS
            const divisionDetails = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.DIVISION_DETAILS,
              useCompanyDetailsStore().divisionDetails,
            );
            broadcast.postMessage(divisionDetails);

            // Operations Channels
            // Full list
            const operationsChannelsList = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.OPERATIONS_CHANNEL_LIST,
              useFilterStore().operationsChannels,
            );
            broadcast.postMessage(operationsChannelsList);

            // Current selected operations channel ids
            const selectedOperationsChannels = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.OPERATIONS_CHANNEL_UPDATED,
              useFilterStore().selectedOperationsChannelIds,
            );
            broadcast.postMessage(selectedOperationsChannels);

            const activeUserName = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.ACTIVE_USER_NAME,
              sessionManager.getActiveUser(),
            );
            broadcast.postMessage(activeUserName);

            const initialDateFilter = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.INITIAL_DATE_FILTER,
              jobListDateFilter.value,
            );
            broadcast.postMessage(initialDateFilter);

            const mainWindowRoute = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.ROUTE_CHANGED,
              route ? route : '',
            );
            broadcast.postMessage(mainWindowRoute);
            const fullJobList = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.FULL_JOB_LIST,
              useJobStore().operationJobsList,
            );
            broadcast.postMessage(fullJobList);
            const unassignedPudItemList = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.UNASSIGNED_PUD_ITEM_LIST,
              useDataImportStore().unassignedPudListForToday,
            );
            broadcast.postMessage(unassignedPudItemList);
            const serviceTypesList = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.SERVICE_TYPES_LIST,
              Array.from(useCompanyDetailsStore().serviceTypesMap.values()),
            );
            broadcast.postMessage(serviceTypesList);
            const ownerList = new BroadcastMessage(
              BroadcastIds.JOB_LIST.TO_EXTERNAL.OWNER_LIST,
              useFleetAssetOwnerStore().getOwnerList,
            );
            broadcast.postMessage(ownerList);
          } else {
            useBroadcastChannelStore().closeChannel(
              BroadcastChannelType.JOB_LIST,
            );
          }
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.SELECTED_JOB_ID:
          Mitt.emit('closeDashboardJobRows', 'popout-job-list');
          setSelectedJobId(message.data.value);
          getFullJobDetails(message.data.value);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.PREALLOCATE_FROM_WINDOW:
          sendPreAllocationRequestFromWindow(
            message.data.value as AllocateJobRequest,
          );
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.GET_FLEET_RATES_FROM_WINDOW:
          // Request service rates from popout window for Preallocation
          const allocationRateRequest: AllocationRateRequest =
            message.data.value;
          getActiveFleetAssetServiceRatesFromWindow(allocationRateRequest);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.ALLOCATE_PREALLOCATED_JOB:
          allocationStore.allocatePreAllocatedJobIds(message.data.value);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.DEALLOCATE_JOB:
          jobStore.updateJobStatus(
            message.data.value,
            JobEventType.DeallocateJob,
          );
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.ADD_NOTE_TO_JOB:
          setSelectedJobId(message.data.value.jobId);
          setViewingJobNotesDialog(true);
          setJobServiceFailure(message.data.value.serviceFailure);
          setJobCancellation(message.data.value.cancelJob);
          setDispatchNote(message.data.value.dispatchNote);
          setStartOfDayCheckNote(message.data.value.startOfDayCheckNote);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.SEND_ADD_NOTE_REQUEST:
          sendAddNoteToJobRequestFromWindow(message.data.value);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.SEND_MESSAGE_TO_DRIVER:
          if (!message.data.value) {
            return;
          }
          setSelectedJobId(message.data.value);
          setSelectedJobDetailsDialogTab('MSG');
          setViewingJobDetailsDialog(true);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.VIEW_SELECTED_JOB_IN_REVIEW:
          if (!message.data.value) {
            return;
          }
          setSelectedJobId(message.data.value);
          setSelectedJobDetailsDialogTab('PRI');
          setViewingJobDetailsDialog(true);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.VIEW_JOB_DETAILS_DIALOG:
          if (!message.data.value) {
            return;
          }
          setSelectedJobId(message.data.value);
          setViewingJobDetailsDialog(true);
          break;
        case BroadcastIds.JOB_LIST.TO_MAIN.EDIT_JOB_IN_BOOKING_SCREEN:
          setSelectedBookingScreenJobId(message.data.value);
          break;
      }
    };
  }

  /**
   * Closes all popout windows that are open. This is called when logging out
   * and when the window unloads
   */
  function closeAllPopoutWindows() {
    const fleetMapStore = useFleetMapStore();
    // If the fleet tracking window is open, send a message to tell it to close
    // itself
    if (fleetMapStore.isFleetTrackingWindowOpen) {
      fleetMapStore.setFleetTrackingWindow(false);
      // Send closed message then close channel
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.FLEET_TRACKING,
        new BroadcastMessage('closeWindow'),
      );
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.FLEET_TRACKING,
        new BroadcastMessage('windowClosed'),
      );
      useBroadcastChannelStore().closeChannel(
        BroadcastChannelType.FLEET_TRACKING,
      );
    }
    // If the job list window is open, send a message to tell it to close
    // itself
    if (operationOptions.value.jobListWindowOpen) {
      setJobListWindow(false);
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.JOB_LIST,
        new BroadcastMessage('closeWindow'),
      );
      useBroadcastChannelStore().closeChannel(BroadcastChannelType.JOB_LIST);
    }
  }
  return {
    selectedFleetAssetId,
    selectedDriverId,
    selectedJobId,
    operationOptions,
    selectedBookingScreenJobId,
    editingCommunicationInDialog,
    viewingJobNotesDialog,
    viewingJobDetailsDialog,
    viewingOutsideHireDetailsDialog,
    viewingAssetInformationDialog,
    viewingPudMaintenanceDialog,
    viewingPudSearchDialog,
    pudMaintenanceDialogConfig,
    bookJobWithUnassignedPudConfig,
    jobListDateFilter,
    isJobServiceFailure,
    isJobCancellation,
    isJobReleaseForEditing,
    isRestoreCancelledJob,
    isDispatchNote,
    isStartOfDayCheckNote,
    releaseForEditingRequestMade,

    reloadJobInBookingScreen,
    reloadUnassignedPudJobInBookingScreen,
    selectedFleetAssetDialogTab,
    selectedJobDetailsDialogTab,
    selectedJobDetails,
    dashboardSettings,
    selectedJobListStatusGrouping,
    operationsBroadcastId,
    resetState,
    setJobListWindow,
    setSelectedJobId,
    setJobListDateFilter,
    setReleaseForEditingRequestMade,
    setSelectedFleetAssetId,
    setSelectedDriverId,
    bookNewJob,
    setSelectedBookingScreenJobId,
    setSelectedJobDetailsDialogTab,
    setSelectedFleetAssetDialogTab,
    setViewingJobNotesDialog,
    setViewingOutsideHireDetailsDialog,
    setEditingCommunicationInDialog,
    setJobServiceFailure,
    setJobReleaseForEditing,
    setJobCancellation,
    setRestoreCancelledJob,
    setDispatchNote,
    setStartOfDayCheckNote,
    setViewingJobDetailsDialog,
    setViewingPudMaintenanceDialog,
    setViewingPudSearchDialog,
    setPudMaintenanceDialogConfig,
    setBookJobWithUnassignedPudConfig,
    setViewingAssetInformationDialog,
    setFullJobDetails,
    getFullJobDetails,
    requestJobDetailsByJobId,
    publishUpdatedJobToWindow,
    getActiveFleetAssetServiceRatesFromWindow,
    updateDashboardSettingsJobList,
    updateDashboardSettingsAllocatedWork,
    updateDashboardSettingsJobInformation,
    updateDashboardSettingsFleetAssetList,
    updateDashboardSettingsDriverMessage,
    isMessageListSettingActive,
    updateJobListStatusGrouping,
    createBroadcastChannel,
    closeAllPopoutWindows,
    getFleetAssetServiceRatesForAllocation,
    reBookJobBookingPageConfig,
    setReBookJobBookingPageConfig,
  };
});
