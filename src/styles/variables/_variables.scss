// ######### Custom Colors ################
// main color
$primary: #f17d2a;
$primary-light: #f8a145;
$primary-dark: #be5200;
$primaryGradient: linear-gradient(60deg, #fb5930, $primary-light);
// accent primary
$accent: #82b1ff;
$accent-secondary: #7decd0;
$success-secondary: #4dcf74;

// highlights primary
$highlight: #64c3ff;
$highlight-dark: #34a1e4;
$highlightgradient: linear-gradient(60deg, #1976d2, $highlight);

$secondary: #424242;

///////////////////// VUETIFY //?////////////////////
// main bg
$app-dark-primary-100: #16161a;
// bg-color nav bar, app bar
$app-dark-primary-200: #1c1c1f;
// secondary main bg
$app-dark-primary-250: #1e1e27;
// alternate bg on secondary main
$app-dark-primary-300: #24232a;
// elements bg color
$app-dark-primary-400: #29282e;
// cards bg color
$app-dark-primary-450: #2a282e9c;
// table row alternate odd row color

$app-dark-primary-500: #302f35;

$app-dark-primary-550: #353239;
// dark highlight
$app-dark-primary-600: #3e3e4e;

$app-dark-primary-650: #8c8ca7;

// text colors
$primary-text-color: #f2f2f2;
$light-text-color: #babcd1;
$text-color-dark: #4d4d4d;

// chips client, fleet dashboard
$group-chip-color: #9fb5d7;
$related-group-chip-color: #a99fd7;

$busy-color: #ff8787;

// light highlight color
$bg-light: #efe0d2;

$bg-light-green: #e0efd2;
$bg-light-red: #efd2d2;
$bg-light-yellow: #efebd2;
$bg-light-blue: #d2d5ef;

// highlight heading color
$heading-text-color: #b2aed3;

// table colors
$table-overflow: #616161;
$table-bg: #312f35;
$table-row-bg: rgba(140, 140, 167, 0.08);

// border colors
$border-darker: #343434;
$border-dark: #272727;
$border-color: rgba(161, 167, 193, 0.5);
$border-light: #38384e;
$border-color-alternate: #8d8d8d;

$sub-text: #c4a9da;

// disabled color
$enabled-color: #47464e;
$disabled-bg: rgba(46, 44, 40, 0.698);

// bar colors
$bar-color: #692121;
$bar-border: #312e29;

// shadow colors
$shadow-color: rgba(18, 18, 20, 0.455);
$shadow-color-dark: rgba(0, 4, 15, 0.35);
$shadow-primary: 4.3px 8.6px 8.6px hsl(0deg 0% 0% / 0.3);

// border-color main
$translucent: rgba(255, 255, 255, 0.086);

$translucent-bg: rgba(20, 20, 32, 0.075);
$translucent-light: rgba(255, 255, 255, 0.439);
$translucent-highlight: rgba(39, 207, 254, 0.3);

$detail-item-value-bg: rgba(117, 202, 255, 0.086);
$detail-item-value-bg-hover: rgba(180, 216, 238, 0.18);
$highlight-text-button: rgba(228, 238, 255, 0.086);

$summary-bg: rgba(164, 164, 197, 0.075);

$bg-overlay: #0a0a12;

// PUD COLORS
$pickup: #55bf59;
$pickup-color: linear-gradient(65deg, $pickup, #206123);
$pickup-highlight: rgb(82, 255, 140);

$drop: #f90070;
$drop-color: linear-gradient(65deg, $drop, #90004d);
$drop-highlight: #ff3781;

$permanentBtn: $primaryGradient;
$adhocBtn: $highlightgradient;

$pud-flag: #ff9800;

// Toasts colors
$toast-info-bg: #1e90f5;
$toast-info-border: #004988;
$toast-info-text: #194877;

$toast-error-bg: #e10909;
$toast-error-border: #a00d0d;
$toast-error-text: #6e0000;

$toast-warning-bg: #ff8f00;
$toast-warning-border: #d85e00;
$toast-warning-text: #875f00;

$toast-success-bg: #43a047;
$toast-success-border: #257129;
$toast-success-text: #00521f;

// status colors
$average: #ffee00;
$success: #33b679;
$warning: #ffc107;
$info: #1976d2;
$error: #ff5252;

$error-type: #e11919;

$warning-type: #faaf00;

$success-type: #00e676;

$outline-type: #00aeff;

$a: rgba(28, 136, 71, 0.516);

$active-state: #0dffa2;

$untracked-job-type: #b044fd;
$swatch: #500f6a;

// Client Portal
$light-theme-text: #2f2e54;
$border-light-client: #c2c2d7;
$client-theme-border: #c1c1b1;
$custom-input-label: #a6a6af;
$client-hover: #333333;
$client-border: #5a5143;
$client-portal-text-color: #5e5e5e;
//  End CLient Portal

// light-dark theme colors
:root {
  --secondary: #{$secondary};
  --error: #{$error};
  --info: #{$info};
  --success: #{$success-type};
  --warning: #{$warning};
  --text-color-light: #{$primary-text-color};
  --text-color-dark: #{$text-color-dark};
  --primary: #f17d2a;
  --primary-light: #f8a145;
  --primary-dark: #be5200;
  --primary-gradient: linear-gradient(60deg, #fb5930, #f8a145);
}

.dark-theme {
  --highlight: #{$highlight};
  --chip-color: #{$toast-info-text};
  --accent: #{$accent};
  --warning: #{$warning};
  --text-color-reverse: #{$app-dark-primary-300};
  --heading-text-color: #{$heading-text-color};
  --accent-secondary: #{$accent-secondary};
  --success-secondary: #{$success-secondary};
  --text-color: #{$primary-text-color};
  --bg-light-blue: #{$bg-light-blue};
  --light-text-color: #{$light-text-color};
  --disabled-text-color: #a9abba;
  --background-color: #{$app-dark-primary-100};
  --background-color-100: #{$app-dark-primary-100};
  --background-color-200: #{$app-dark-primary-200};
  --background-color-250: #{$app-dark-primary-250};
  --background-color-300: #{$app-dark-primary-300};
  --background-color-400: #{$app-dark-primary-400};
  --background-color-card: #{$app-dark-primary-400};
  --background-color-500: #{$app-dark-primary-500};
  --background-color-550: #{$app-dark-primary-500};
  --background-color-600: #{$app-dark-primary-600};
  --background-color-650: #{$app-dark-primary-650};
  --table-bg-100: #{$table-bg};
  --border-color: #{$translucent};
  --border-color-100: #{$border-dark};
  --bg-light: #{$bg-light};
  --box-shadow: #{$shadow-primary};
  --shadow-color: rgba(0, 0, 0, 0.45);
  --yellow: yellow;
  --overlay-background: hsla(240, 5%, 12%, 0.8);
  --table-row: #{$table-row-bg};
  --bg-light-green: #{$bg-light-green};
  --bg-light-red: #{$bg-light-red};
  --bg-light-yellow: #{$bg-light-yellow};
  --bg-light-blue: #{$bg-light-blue};
  --hover-bg: #{$light-theme-text};
  --group-chip-color: #{$group-chip-color};
  --related-group-chip-color: #{$related-group-chip-color};
}

.light-theme {
  --highlight: #{$highlight-dark};
  --chip-color: #9accff;
  --accent: #007aff;
  --warning: #c09000;
  --text-color-reverse: #fafafa;
  --heading-text-color: #65658e;
  --accent-secondary: #39b797;
  --success-secondary: #{$success-secondary};
  --bg-light-blue: #{$light-theme-text};
  --text-color: #121212;
  --light-text-color: #636366;
  --disabled-text-color: #87878d;
  --background-color: #fafafa;
  --background-color-100: #fafafa; /* small card bg */
  --background-color-200: #f7f7f7; /* big card bg */
  --background-color-250: #e6e6e6;
  --background-color-300: #fafafa; /* inputs */
  --background-color-400: #ffffff; /* primary bg */
  --background-color-500: #f4f4f4; /* notes on card bg */
  --background-color-550: #e6e6e6; /* loading inputs */
  --background-color-600: #dfe6ea; /* table hover */
  --background-color-650: #e9ebec;
  --background-color-card: #f5f5f5;
  --table-bg-100: #f7f7f7;
  --border-color: #{$border-color};
  --border-color-100: rgba(0, 0, 0, 0.086);
  --bg-light: #{$light-theme-text};
  --box-shadow: 6px 8px 14px hsla(0, 0%, 32%, 0.2);
  --shadow-color: rgba(221, 221, 221, 0.45);
  --yellow: #daa521;
  --overlay-background: rgba(233, 235, 236, 0.7);
  --table-row: rgba(149, 148, 167, 0.2);
  --bg-light-green: #54683f;
  --bg-light-red: #653131;
  --bg-light-yellow: #757050;
  --bg-light-blue: #404762;
  --hover-bg: #{$bg-light-blue};
  --group-chip-color: #6582ad;
  --related-group-chip-color: #877cb6;
}
// ######### END Custom COLORS ################

// ######### Elements ################
$app-bar-height: 40px;
$app-nav-bar-width: 50px;
$user-portal-desktop-breakpoint: 950px;

$x-padding: 14px;
$y-padding: 14px;
$divider-offset: 20px;

$dialog-content-padding: 20px;

// Shadows
$mdb-shadow-key-umbra-opacity: 0.2 !default;
$mdb-shadow-key-penumbra-opacity: 0.14 !default;
$mdb-shadow-ambient-shadow-opacity: 0.12 !default;
// $box-shadow: inset 4 4 10px $shadow-color;
$box-shadow: $shadow-primary;
$box-shadow-lg: 0px 20px 20px -20px var(--box-shadow);
$box-shadow-dark: 6px 20px 20px -16px $shadow-color-dark;

// Border Radius
$border-radius-Xsm: 6px;
$border-radius-btn: 10px;
$border-radius-sm: 8px;
$border-radius-base: 12px !default;
$border-radius-lg: 14px;
$border-radius-Xlg: 18px;
// ######### END Elements ################

// ######### Fonts ################
// Font sizes in em
$font-size-small: 0.8em;
$font-size-medium: 0.9em;
$font-size-large: 1.1em;
$font-size-Xlarge: 2em;

// Font sizes in pixels
$font-size-10: 10px;
$font-size-11: 11px;
$font-size-12: 12px;
$font-size-13: 13px;
$font-size-14: 14px;
$font-size-15: 15px;
$font-size-16: 16px;
$font-size-17: 17px;
$font-size-18: 18px;
$font-size-20: 20px;
$font-size-22: 22px;
$font-size-24: 24px;

// fonts family
@import url('https://fonts.googleapis.com/css?family=Archivo');
@import url('https://fonts.googleapis.com/css?family=Raleway');

$font-awesome-family: 'Font Awesome 5 Pro', Helvetica, sans-serif;
$font-sans: 'Open Sans', Helvetica, sans-serif;
$sub-font-family: 'Archivo', Helvetica, sans-serif;
$font-family: 'Raleway', Helvetica, Arial, sans-serif;
$app-font-family: 'Roboto', Helvetica, Arial, sans-serif;
$font-family-alternate: 'Titillium Web', sans-serif;

// ######### End Fonts ################
